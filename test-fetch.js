fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent', {
  method: 'POST',
  headers: {
    'x-goog-api-key': process.env.GEMINI_API_KEY,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    contents: [
      {
        parts: [
          { text: 'Explain how AI works in a few words' }
        ]
      }
    ]
  })
})
  .then(res => res.json())
  .then(console.log)
  .catch(console.error);