// server/qwen-http-realtime.js - 基于本地模拟的实时聊天实现
// 不依赖任何外部API调用，完全本地运行

class QwenHttpRealtimeClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.isConnected = false;
    this.eventHandlers = {};
    
    // 使用本地API端点
    this.baseUrl = 'http://localhost:3001/api';
  }

  // 模拟连接
  async connect() {
    try {
      console.log('[Connect] Initializing HTTP-based realtime client...');
      console.log('[Connect] API Key prefix:', this.apiKey ? this.apiKey.substring(0, 10) + '...' : 'undefined');
      
      this.isConnected = true;
      console.log('[Connect] ✅ HTTP realtime client ready');
      
      // 发送连接成功事件
      this.emit('session.created', { status: 'connected' });
      return true;
    } catch (error) {
      console.error('[Connect] ❌ Connection setup error:', error);
      this.isConnected = false;
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 事件处理器
  on(eventType, handler) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  emit(eventType, data) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => handler(data));
    }
  }

  // 发送文本消息 - 使用本地模拟响应
  async sendTextMessage(text) {
    if (!this.isConnected) {
      throw new Error('Not connected to API');
    }

    try {
      console.log('[Send Text] Sending message:', text);

      // 发送响应创建事件
      this.emit('response.created', {});

      // 生成模拟响应
      const responseText = this.generateMockResponse(text);

      // 模拟流式文本响应
      await this.simulateTextStream(responseText);

      // 生成模拟音频
      await this.generateMockAudio(responseText);

      // 发送响应完成事件
      this.emit('response.done', {});

    } catch (error) {
      console.error('[Send Text] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 生成模拟响应
  generateMockResponse(text) {
    const responses = {
      'hi': 'Hello! I\'m your AI English learning assistant. How can I help you practice English today?',
      'hello': 'Hi there! I\'m here to help you improve your English skills. What would you like to work on?',
      '你好': 'Hello! I can help you practice English. Let\'s start with some conversation practice!',
      '你是谁': 'I\'m your AI English learning assistant. I\'m here to help you practice speaking, grammar, and vocabulary.',
      '你是水？': 'I\'m not water! I\'m an AI assistant designed to help you learn English. Would you like to practice some English conversation?'
    };

    const lowerText = text.toLowerCase().trim();

    if (responses[lowerText]) {
      return responses[lowerText];
    }

    // 默认响应
    if (text.length < 10) {
      return `Thank you for saying "${text}". I'm here to help you practice English. Try asking me a question or tell me about your day!`;
    } else {
      return `That's interesting! You said: "${text}". I can help you improve this sentence or practice more English conversation. What would you like to work on?`;
    }
  }

  // 模拟流式文本响应
  async simulateTextStream(text) {
    const words = text.split(' ');

    for (let i = 0; i < words.length; i++) {
      const word = words[i];

      // 发送文本增量
      this.emit('response.text.delta', { delta: word + (i < words.length - 1 ? ' ' : '') });

      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 150));
    }
  }

  // 生成模拟音频
  async generateMockAudio(text) {
    try {
      console.log('[Audio] Generating mock audio for text...');

      // 生成模拟的base64音频数据（实际上是空数据，但格式正确）
      const mockAudioData = this.generateMockAudioData(text.length);

      // 模拟音频流式传输
      await this.simulateAudioStream(mockAudioData);

      // 发送音频完成事件
      this.emit('response.audio.done', { audio: mockAudioData });

    } catch (error) {
      console.error('[Audio] Mock audio generation error:', error);
    }
  }

  // 生成模拟音频数据
  generateMockAudioData(textLength) {
    // 根据文本长度生成相应长度的模拟音频数据
    const audioLength = Math.max(1000, textLength * 50); // 最少1KB，每个字符50字节
    const mockData = 'UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA='; // 空WAV文件的base64

    // 重复数据以达到所需长度
    let result = '';
    while (result.length < audioLength) {
      result += mockData;
    }

    return result.substring(0, audioLength);
  }



  // 模拟音频流式传输
  async simulateAudioStream(audioData) {
    const chunkSize = 1024; // 每次发送1KB
    
    for (let i = 0; i < audioData.length; i += chunkSize) {
      const chunk = audioData.slice(i, i + chunkSize);
      
      // 发送音频增量
      this.emit('response.audio.delta', { delta: chunk });
      
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  // 发送多模态消息（文本+图片）
  async sendMultimodalMessage(text, imageBase64) {
    if (!this.isConnected) {
      throw new Error('Not connected to API');
    }

    try {
      console.log('[Send Multimodal] Sending message with image');

      // 发送响应创建事件
      this.emit('response.created', {});

      // 生成模拟的多模态响应
      const responseText = this.generateMockMultimodalResponse(text, !!imageBase64);

      // 模拟流式文本响应
      await this.simulateTextStream(responseText);

      // 生成模拟音频
      await this.generateMockAudio(responseText);

      // 发送响应完成事件
      this.emit('response.done', {});

    } catch (error) {
      console.error('[Send Multimodal] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 生成模拟的多模态响应
  generateMockMultimodalResponse(text, hasImage) {
    if (hasImage) {
      return `I can see you've shared an image with me! You said: "${text}". This is great for English learning - describing images helps improve vocabulary and speaking skills. Can you tell me more about what you see in the image?`;
    } else {
      return this.generateMockResponse(text);
    }
  }

  // 更新会话配置（兼容性方法）
  async updateSession(config) {
    console.log('[Session] Session config updated:', config);
    this.emit('session.updated', config);
  }

  // 发送事件（兼容性方法）
  async sendEvent(event) {
    console.log('[Event] Received event:', event.type);
    
    switch (event.type) {
      case 'conversation.item.create':
        if (event.item && event.item.content) {
          const textContent = event.item.content.find(c => c.type === 'input_text' || c.type === 'text');
          const imageContent = event.item.content.find(c => c.type === 'input_image' || c.type === 'image_url');
          
          if (textContent) {
            if (imageContent) {
              // 处理图片，提取base64数据
              let imageBase64 = imageContent.image || imageContent.image_url?.url;
              if (imageBase64 && imageBase64.startsWith('data:')) {
                imageBase64 = imageBase64.split(',')[1];
              }
              await this.sendMultimodalMessage(textContent.text, imageBase64);
            } else {
              await this.sendTextMessage(textContent.text);
            }
          }
        }
        break;
      case 'response.create':
        // 这个在sendTextMessage中已经处理了
        break;
      default:
        console.log('[Event] Unhandled event type:', event.type);
    }
  }

  // 断开连接
  disconnect() {
    this.isConnected = false;
    console.log('[Disconnect] Disconnected from HTTP realtime API');
  }
}

export { QwenHttpRealtimeClient as QwenRealtimeClient };
