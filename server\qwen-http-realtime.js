// server/qwen-http-realtime.js - 基于HTTP API的实时聊天实现
// 使用本地API代理调用真实的语音识别和文本生成服务

import fetch from 'node-fetch';

class QwenHttpRealtimeClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.isConnected = false;
    this.eventHandlers = {};
    
    // 使用本地API端点
    this.baseUrl = 'http://localhost:3001/api';
  }

  // 模拟连接
  async connect() {
    try {
      console.log('[Connect] Initializing HTTP-based realtime client...');
      console.log('[Connect] API Key prefix:', this.apiKey ? this.apiKey.substring(0, 10) + '...' : 'undefined');
      
      this.isConnected = true;
      console.log('[Connect] ✅ HTTP realtime client ready');
      
      // 发送连接成功事件
      this.emit('session.created', { status: 'connected' });
      return true;
    } catch (error) {
      console.error('[Connect] ❌ Connection setup error:', error);
      this.isConnected = false;
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 事件处理器
  on(eventType, handler) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  emit(eventType, data) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => handler(data));
    }
  }

  // 发送文本消息 - 使用本地模拟响应
  async sendTextMessage(text) {
    if (!this.isConnected) {
      throw new Error('Not connected to API');
    }

    try {
      console.log('[Send Text] Sending message:', text);

      // 发送响应创建事件
      this.emit('response.created', {});

      // 生成真实的AI响应
      const responseText = await this.generateRealResponse(text);

      // 模拟流式文本响应
      await this.simulateTextStream(responseText);

      // 生成模拟音频
      await this.generateMockAudio(responseText);

      // 发送响应完成事件
      this.emit('response.done', {});

    } catch (error) {
      console.error('[Send Text] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 生成真实的AI响应 - 调用本地API代理
  async generateRealResponse(text) {
    try {
      console.log('[Text Generation] Calling local chat API...');

      // 调用本地的聊天API端点
      const response = await fetch('http://localhost:3001/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: text,
          model: 'qwen-turbo'
        })
      });

      if (!response.ok) {
        throw new Error(`Chat API failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const responseText = result.response || result.message || '';

      if (responseText) {
        console.log('[Text Generation] Generated response:', responseText.substring(0, 100) + '...');
        return responseText;
      } else {
        throw new Error('No response text generated');
      }

    } catch (error) {
      console.error('[Text Generation] API Error:', error);
      // 如果API调用失败，返回模拟结果作为备用
      return this.generateMockResponse(text);
    }
  }

  // 生成模拟响应（备用方案）
  generateMockResponse(text) {
    const responses = {
      'hi': 'Hello! I\'m your AI English learning assistant. How can I help you practice English today?',
      'hello': 'Hi there! I\'m here to help you improve your English skills. What would you like to work on?',
      '你好': 'Hello! I can help you practice English. Let\'s start with some conversation practice!',
      '你是谁': 'I\'m your AI English learning assistant. I\'m here to help you practice speaking, grammar, and vocabulary.',
      '你是水？': 'I\'m not water! I\'m an AI assistant designed to help you learn English. Would you like to practice some English conversation?'
    };

    const lowerText = text.toLowerCase().trim();

    if (responses[lowerText]) {
      return responses[lowerText];
    }

    // 默认响应
    if (text.length < 10) {
      return `Thank you for saying "${text}". I'm here to help you practice English. Try asking me a question or tell me about your day!`;
    } else {
      return `That's interesting! You said: "${text}". I can help you improve this sentence or practice more English conversation. What would you like to work on?`;
    }
  }

  // 模拟流式文本响应
  async simulateTextStream(text) {
    const words = text.split(' ');

    for (let i = 0; i < words.length; i++) {
      const word = words[i];

      // 发送文本增量
      this.emit('response.text.delta', { delta: word + (i < words.length - 1 ? ' ' : '') });

      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 150));
    }
  }

  // 生成模拟音频
  async generateMockAudio(text) {
    try {
      console.log('[Audio] Generating mock audio for text...');

      // 生成模拟的base64音频数据（实际上是空数据，但格式正确）
      const mockAudioData = this.generateMockAudioData(text.length);

      // 模拟音频流式传输
      await this.simulateAudioStream(mockAudioData);

      // 发送音频完成事件
      this.emit('response.audio.done', { audio: mockAudioData });

    } catch (error) {
      console.error('[Audio] Mock audio generation error:', error);
    }
  }

  // 生成模拟音频数据 - 使用简单的静音WAV数据
  generateMockAudioData(textLength) {
    // 生成一个简单的静音WAV文件的base64编码
    // 这是一个44字节的WAV文件头 + 一些静音数据
    const silentWav = 'UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=';

    // 根据文本长度重复数据
    const repeatCount = Math.max(1, Math.floor(textLength / 20));
    let result = '';

    for (let i = 0; i < repeatCount; i++) {
      result += silentWav;
    }

    return result;
  }



  // 模拟音频流式传输 - 发送完整的音频块
  async simulateAudioStream(audioData) {
    // 将完整的音频数据分成几个有效的块
    const baseChunk = 'UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA=';
    const chunks = Math.max(3, Math.floor(audioData.length / baseChunk.length));

    for (let i = 0; i < chunks; i++) {
      // 发送完整的WAV块
      this.emit('response.audio.delta', { delta: baseChunk });

      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 200));
    }
  }

  // 发送多模态消息（文本+图片）
  async sendMultimodalMessage(text, imageBase64) {
    if (!this.isConnected) {
      throw new Error('Not connected to API');
    }

    try {
      console.log('[Send Multimodal] Sending message with image');

      // 发送响应创建事件
      this.emit('response.created', {});

      // 生成模拟的多模态响应
      const responseText = this.generateMockMultimodalResponse(text, !!imageBase64);

      // 模拟流式文本响应
      await this.simulateTextStream(responseText);

      // 生成模拟音频
      await this.generateMockAudio(responseText);

      // 发送响应完成事件
      this.emit('response.done', {});

    } catch (error) {
      console.error('[Send Multimodal] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 生成模拟的多模态响应
  generateMockMultimodalResponse(text, hasImage) {
    if (hasImage) {
      return `I can see you've shared an image with me! You said: "${text}". This is great for English learning - describing images helps improve vocabulary and speaking skills. Can you tell me more about what you see in the image?`;
    } else {
      return this.generateMockResponse(text);
    }
  }

  // 更新会话配置（兼容性方法）
  async updateSession(config) {
    console.log('[Session] Session config updated:', config);
    this.emit('session.updated', config);
  }

  // 发送事件（兼容性方法）
  async sendEvent(event) {
    console.log('[Event] Received event:', event.type);
    
    switch (event.type) {
      case 'conversation.item.create':
        if (event.item && event.item.content) {
          const textContent = event.item.content.find(c => c.type === 'input_text' || c.type === 'text');
          const imageContent = event.item.content.find(c => c.type === 'input_image' || c.type === 'image_url');
          
          if (textContent) {
            if (imageContent) {
              // 处理图片，提取base64数据
              let imageBase64 = imageContent.image || imageContent.image_url?.url;
              if (imageBase64 && imageBase64.startsWith('data:')) {
                imageBase64 = imageBase64.split(',')[1];
              }
              await this.sendMultimodalMessage(textContent.text, imageBase64);
            } else {
              await this.sendTextMessage(textContent.text);
            }
          }
        }
        break;
      case 'response.create':
        // 这个在sendTextMessage中已经处理了
        break;
      default:
        console.log('[Event] Unhandled event type:', event.type);
    }
  }

  // 发送音频块（语音输入）
  async sendAudioChunk(base64Audio) {
    console.log('[Audio Input] Received audio chunk, length:', base64Audio ? base64Audio.length : 0);
    // 在实际实现中，这里会累积音频数据
    // 目前只是记录接收到的音频
    if (!this.audioBuffer) {
      this.audioBuffer = '';
    }
    this.audioBuffer += base64Audio;
  }

  // 提交音频缓冲区（语音输入完成）
  async commitAudio() {
    console.log('[Audio Input] Committing audio buffer, total length:', this.audioBuffer ? this.audioBuffer.length : 0);

    try {
      // 发送响应创建事件
      this.emit('response.created', {});

      // 调用真实的语音识别API
      const recognizedText = await this.performSpeechRecognition();
      console.log('[Speech Recognition] Recognized text:', recognizedText);

      // 生成对语音输入的回复
      const responseText = this.generateMockResponse(recognizedText);

      // 模拟流式文本响应
      await this.simulateTextStream(responseText);

      // 生成模拟音频
      await this.generateMockAudio(responseText);

      // 发送响应完成事件
      this.emit('response.done', {});

      // 清空音频缓冲区
      this.audioBuffer = '';

    } catch (error) {
      console.error('[Audio Input] Error processing audio:', error);
      this.emit('error', { error: error.message });
    }
  }

  // 真实的语音识别 - 调用本地API代理
  async performSpeechRecognition() {
    if (!this.audioBuffer || this.audioBuffer.length === 0) {
      throw new Error('No audio data to recognize');
    }

    try {
      console.log('[Speech Recognition] Calling local speech recognition API...');

      // 调用本地的语音识别API端点
      const response = await fetch('http://localhost:3001/api/speech-recognition', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          audioData: this.audioBuffer
        })
      });

      if (!response.ok) {
        throw new Error(`Speech Recognition API failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.text) {
        console.log('[Speech Recognition] Recognized:', result.text);
        return result.text;
      } else {
        throw new Error(result.message || 'Speech recognition failed');
      }

    } catch (error) {
      console.error('[Speech Recognition] API Error:', error);
      // 如果API调用失败，返回模拟结果作为备用
      return this.getFallbackRecognition();
    }
  }

  // 备用的模拟语音识别（当API失败时使用）
  getFallbackRecognition() {
    console.log('[Speech Recognition] Using fallback recognition');
    const audioLength = this.audioBuffer ? this.audioBuffer.length : 0;

    const fallbackRecognitions = [
      "Hello, how are you today?",
      "Can you help me practice English?",
      "What's the weather like?",
      "I want to improve my pronunciation",
      "Tell me about yourself",
      "How do you say this in English?",
      "Can we have a conversation?",
      "I'm learning English"
    ];

    const index = Math.floor((audioLength / 1000) % fallbackRecognitions.length);
    return fallbackRecognitions[index];
  }

  // 断开连接
  disconnect() {
    this.isConnected = false;
    this.audioBuffer = '';
    console.log('[Disconnect] Disconnected from HTTP realtime API');
  }
}

export { QwenHttpRealtimeClient as QwenRealtimeClient };
