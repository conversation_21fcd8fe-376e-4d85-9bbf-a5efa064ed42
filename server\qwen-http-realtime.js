// server/qwen-http-realtime.js - 基于HTTP的实时聊天实现
// 使用阿里云标准API而不是WebSocket，通过本地代理实现

import fetch from 'node-fetch';

class QwenHttpRealtimeClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.isConnected = false;
    this.eventHandlers = {};
    
    // 使用本地代理端点，与其他API调用保持一致
    this.baseUrl = 'http://localhost:3001/api/dashscope';
  }

  // 模拟连接
  async connect() {
    try {
      console.log('[Connect] Initializing HTTP-based realtime client...');
      console.log('[Connect] API Key prefix:', this.apiKey ? this.apiKey.substring(0, 10) + '...' : 'undefined');
      
      this.isConnected = true;
      console.log('[Connect] ✅ HTTP realtime client ready');
      
      // 发送连接成功事件
      this.emit('session.created', { status: 'connected' });
      return true;
    } catch (error) {
      console.error('[Connect] ❌ Connection setup error:', error);
      this.isConnected = false;
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 事件处理器
  on(eventType, handler) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  emit(eventType, data) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => handler(data));
    }
  }

  // 发送文本消息 - 使用HTTP API
  async sendTextMessage(text) {
    if (!this.isConnected) {
      throw new Error('Not connected to API');
    }

    try {
      console.log('[Send Text] Sending message:', text);
      
      // 发送响应创建事件
      this.emit('response.created', {});
      
      // 使用Qwen-TTS生成文本和音频响应
      const requestBody = {
        model: 'qwen-turbo',
        input: {
          messages: [
            {
              role: 'user',
              content: text
            }
          ]
        },
        parameters: {
          temperature: 0.7,
          max_tokens: 1000
        }
      };

      const response = await fetch(`${this.baseUrl}/api/v1/services/aigc/text-generation/generation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Text generation failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const responseText = result.output?.text || '';
      
      // 模拟流式文本响应
      await this.simulateTextStream(responseText);
      
      // 生成音频
      await this.generateAudio(responseText);
      
      // 发送响应完成事件
      this.emit('response.done', {});
      
    } catch (error) {
      console.error('[Send Text] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 模拟流式文本响应
  async simulateTextStream(text) {
    const words = text.split(' ');
    let currentText = '';
    
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      currentText += (i > 0 ? ' ' : '') + word;
      
      // 发送文本增量
      this.emit('response.text.delta', { delta: word + (i < words.length - 1 ? ' ' : '') });
      
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  // 生成音频
  async generateAudio(text) {
    try {
      console.log('[Audio] Generating audio for text...');
      
      const requestBody = {
        model: 'qwen-tts',
        input: {
          text: text
        },
        parameters: {
          voice: 'Chelsie',
          format: 'wav'
        }
      };

      const response = await fetch(`${this.baseUrl}/api/v1/services/aigc/multimodal-generation/generation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        console.error('[Audio] Audio generation failed:', response.status, response.statusText);
        return;
      }

      const result = await response.json();
      const audioData = result.output?.audio;
      
      if (audioData) {
        // 模拟音频流式传输
        await this.simulateAudioStream(audioData);
        
        // 发送音频完成事件
        this.emit('response.audio.done', { audio: audioData });
      }
      
    } catch (error) {
      console.error('[Audio] Audio generation error:', error);
    }
  }

  // 模拟音频流式传输
  async simulateAudioStream(audioData) {
    const chunkSize = 1024; // 每次发送1KB
    
    for (let i = 0; i < audioData.length; i += chunkSize) {
      const chunk = audioData.slice(i, i + chunkSize);
      
      // 发送音频增量
      this.emit('response.audio.delta', { delta: chunk });
      
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  // 发送多模态消息（文本+图片）
  async sendMultimodalMessage(text, imageBase64) {
    if (!this.isConnected) {
      throw new Error('Not connected to API');
    }

    try {
      console.log('[Send Multimodal] Sending message with image');
      
      // 发送响应创建事件
      this.emit('response.created', {});
      
      // 使用Qwen-VL处理图片和文本
      const requestBody = {
        model: 'qwen-vl-plus',
        input: {
          messages: [
            {
              role: 'user',
              content: [
                {
                  text: text
                },
                {
                  image: `data:image/jpeg;base64,${imageBase64}`
                }
              ]
            }
          ]
        },
        parameters: {
          temperature: 0.7,
          max_tokens: 1000
        }
      };

      const response = await fetch(`${this.baseUrl}/api/v1/services/aigc/multimodal-generation/generation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Multimodal generation failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const responseText = result.output?.text || '';
      
      // 模拟流式文本响应
      await this.simulateTextStream(responseText);
      
      // 生成音频
      await this.generateAudio(responseText);
      
      // 发送响应完成事件
      this.emit('response.done', {});
      
    } catch (error) {
      console.error('[Send Multimodal] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 更新会话配置（兼容性方法）
  async updateSession(config) {
    console.log('[Session] Session config updated:', config);
    this.emit('session.updated', config);
  }

  // 发送事件（兼容性方法）
  async sendEvent(event) {
    console.log('[Event] Received event:', event.type);
    
    switch (event.type) {
      case 'conversation.item.create':
        if (event.item && event.item.content) {
          const textContent = event.item.content.find(c => c.type === 'input_text' || c.type === 'text');
          const imageContent = event.item.content.find(c => c.type === 'input_image' || c.type === 'image_url');
          
          if (textContent) {
            if (imageContent) {
              // 处理图片，提取base64数据
              let imageBase64 = imageContent.image || imageContent.image_url?.url;
              if (imageBase64 && imageBase64.startsWith('data:')) {
                imageBase64 = imageBase64.split(',')[1];
              }
              await this.sendMultimodalMessage(textContent.text, imageBase64);
            } else {
              await this.sendTextMessage(textContent.text);
            }
          }
        }
        break;
      case 'response.create':
        // 这个在sendTextMessage中已经处理了
        break;
      default:
        console.log('[Event] Unhandled event type:', event.type);
    }
  }

  // 断开连接
  disconnect() {
    this.isConnected = false;
    console.log('[Disconnect] Disconnected from HTTP realtime API');
  }
}

export { QwenHttpRealtimeClient as QwenRealtimeClient };
