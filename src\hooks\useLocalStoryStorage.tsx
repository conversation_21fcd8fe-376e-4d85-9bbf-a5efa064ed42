import { useCallback } from 'react';
import { type Story, type LocalStory } from '@/types/story';
import { useLocalAudioStorage } from './useLocalAudioStorage';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Export interface for the audio data
interface StorageDataWithAudio {
  version: string;
  timestamp: string;
  stories: LocalStory[];
  audio: { [key: string]: string };
}

const STORIES_KEY = 'local_stories';
const MAX_STORY_AGE_DAYS = 30; // 存储30天

export const useLocalStoryStorage = () => {
  // 获取所有本地故事
  const getLocalStories = useCallback(() => {
    try {
      const storiesJSON = localStorage.getItem(STORIES_KEY);
      return storiesJSON ? JSON.parse(storiesJSON) as LocalStory[] : [];
    } catch (error) {
      console.error('获取本地故事失败:', error);
      return [];
    }
  }, []);

  // 保存故事到本地
  const saveStoryToLocal = useCallback((story: Story, hasAudio: boolean = false) => {
    try {
      const existingStories = getLocalStories();

      const localStory: LocalStory = {
        ...story,
        hasAudio,
        lastAccessed: new Date().toISOString()
      };

      const storyIndex = existingStories.findIndex(s => s.id === story.id);
      if (storyIndex !== -1) {
        existingStories[storyIndex] = localStory;
      } else {
        existingStories.push(localStory);
      }

      localStorage.setItem(STORIES_KEY, JSON.stringify(existingStories));
    } catch (error) {
      console.error('保存故事到本地失败:', error);
      throw error;
    }
  }, [getLocalStories]);

  // 更新故事的音频状态
  const updateStoryAudioStatus = useCallback((storyId: string, hasAudio: boolean) => {
    try {
      const stories = getLocalStories();
      const storyIndex = stories.findIndex(s => s.id === storyId);
      if (storyIndex !== -1) {
        stories[storyIndex].hasAudio = hasAudio;
        stories[storyIndex].lastAccessed = new Date().toISOString();
        localStorage.setItem(STORIES_KEY, JSON.stringify(stories));
      }
    } catch (error) {
      console.error('更新故事音频状态失败:', error);
      throw error;
    }
  }, [getLocalStories]);

  // 删除故事
  const deleteStory = useCallback((storyId: string) => {
    try {
      const stories = getLocalStories();
      const filteredStories = stories.filter(s => s.id !== storyId);
      localStorage.setItem(STORIES_KEY, JSON.stringify(filteredStories));
    } catch (error) {
      console.error('删除故事失败:', error);
      throw error;
    }
  }, [getLocalStories]);

  // 清理旧故事
  const cleanupOldStories = useCallback(() => {
    try {
      const stories = getLocalStories();
      const now = new Date();
      const maxAge = MAX_STORY_AGE_DAYS * 24 * 60 * 60 * 1000; // 30天的毫秒数

      const recentStories = stories.filter(story => {
        const lastAccessed = new Date(story.lastAccessed);
        return now.getTime() - lastAccessed.getTime() < maxAge;
      });

      if (recentStories.length !== stories.length) {
        localStorage.setItem(STORIES_KEY, JSON.stringify(recentStories));
        console.log(`已清理 ${stories.length - recentStories.length} 个过期故事`);
      }
    } catch (error) {
      console.error('清理旧故事失败:', error);
    }
  }, [getLocalStories]);

  // 获取存储统计信息
  const getStorageStats = useCallback(() => {
    const stories = getLocalStories();
    return {
      totalStories: stories.length,
      storiesWithAudio: stories.filter(s => s.hasAudio).length,
      storageSize: new TextEncoder().encode(JSON.stringify(stories)).length,
      lastCleanup: new Date().getTime()
    };
  }, [getLocalStories]);

  const downloadStoryAsPdfWithAudio = useCallback(async (story: LocalStory, audioBlob: Blob | null) => {
    const element = document.createElement('div');
    element.style.width = '210mm';
    element.style.padding = '20px';
    element.style.fontFamily = 'sans-serif';

    element.innerHTML = `
      <h1 style="font-size: 24px; text-align: center; margin-bottom: 20px;">${story.title}</h1>
      ${story.imageUrl ? `<img src="${story.imageUrl}" style="max-width: 100%; height: auto; margin-bottom: 20px;">` : ''}
      <h2 style="font-size: 18px; margin-top: 20px; border-bottom: 1px solid #eee; padding-bottom: 5px;">English Story</h2>
      <p style="font-size: 14px; line-height: 1.6;">${story.story.replace(/\n/g, '<br>')}</p>
      <h2 style="font-size: 18px; margin-top: 20px; border-bottom: 1px solid #eee; padding-bottom: 5px;">中文翻译</h2>
      <p style="font-size: 14px; line-height: 1.6;">${story.storyChinese?.replace(/\n/g, '<br>') || '无翻译'}</p>
    `;
    document.body.appendChild(element);

    const canvas = await html2canvas(element, { scale: 2 });
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = (canvas.height * pdfWidth) / canvas.width;
    pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, 0, pdfWidth, pdfHeight);
    
    if (audioBlob) {
      try {
        const audioBase64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(audioBlob);
        });

        // 1. Add file to virtual file system
        const fileName = `${story.title.replace(/[^a-z0-9]/gi, '_')}.wav`;
        pdf.addFileToVFS(fileName, audioBase64.split(',')[1]);

        // 2. Create a file attachment annotation
        // We'll place a small, clickable icon at the top-right
        pdf.addPage();
        pdf.text('Attachment', 10, 10);
        pdf.fileAnnotation(10, 15, 10, 10, {
          file: fileName,
          name: 'Play Audio',
          description: story.title
        });
        pdf.text('Click the icon above to play the audio for the story.', 10, 30);

      } catch (e) {
        console.error("Failed to attach audio to PDF:", e);
        // Optionally notify the user that audio attachment failed
      }
    }

    pdf.save(`${story.title}.pdf`);
    document.body.removeChild(element);
  }, []);

  // 导出故事数据
  const exportStoriesData = useCallback(() => {
    try {
      const stories = getLocalStories();
      return JSON.stringify({
        version: "1.0",
        timestamp: new Date().toISOString(),
        stories
      }, null, 2);
    } catch (error) {
      console.error('导出故事数据失败:', error);
      throw error;
    }
  }, [getLocalStories]);

  // 导入故事数据
  const importStoriesData = useCallback((jsonData: string) => {
    try {
      const data = JSON.parse(jsonData);
      if (!data.stories || !Array.isArray(data.stories)) {
        throw new Error('无效的数据格式');
      }

      // 验证故事数据格式
      for (const story of data.stories) {
        if (!story.id || !story.title || !story.content) {
          throw new Error('故事数据不完整');
        }
      }

      // 合并现有数据
      const existingStories = getLocalStories();
      const mergedStories = [...existingStories];
      
      for (const story of data.stories) {
        const index = mergedStories.findIndex(s => s.id === story.id);
        if (index !== -1) {
          mergedStories[index] = {
            ...story,
            lastAccessed: new Date().toISOString()
          };
        } else {
          mergedStories.push({
            ...story,
            lastAccessed: new Date().toISOString()
          });
        }
      }

      localStorage.setItem(STORIES_KEY, JSON.stringify(mergedStories));
      return true;
    } catch (error) {
      console.error('导入故事数据失败:', error);
      return false;
    }
  }, [getLocalStories]);

  // 导出带音频的故事数据
  const exportStoriesWithAudio = useCallback(async (audioStorage: ReturnType<typeof useLocalAudioStorage>) => {
    try {
      const stories = getLocalStories();
      const audioStories = stories.filter(story => story.hasAudio);
      
      // 获取每个故事的音频
      const audioData: { [key: string]: string } = {};
      for (const story of audioStories) {
        const audioBlob = await audioStorage.getAudioBlob(story.id);
        if (audioBlob) {
          // 转换为 base64
          const buffer = await audioBlob.arrayBuffer();
          const base64 = btoa(
            new Uint8Array(buffer).reduce((data, byte) => data + String.fromCharCode(byte), '')
          );
          audioData[story.id] = base64;
        }
      }

      const exportData: StorageDataWithAudio = {
        version: "1.1",
        timestamp: new Date().toISOString(),
        stories,
        audio: audioData
      };

      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('导出故事和音频数据失败:', error);
      throw error;
    }
  }, [getLocalStories]);

  // 导入带音频的故事数据
  const importStoriesWithAudio = useCallback(async (
    jsonData: string,
    audioStorage: ReturnType<typeof useLocalAudioStorage>
  ) => {
    try {
      const data = JSON.parse(jsonData) as StorageDataWithAudio;
      if (!data.stories || !Array.isArray(data.stories)) {
        throw new Error('无效的数据格式');
      }

      // 导入故事
      const success = importStoriesData(JSON.stringify({ stories: data.stories }));
      if (!success) {
        throw new Error('导入故事失败');
      }

      // 导入音频
      if (data.audio) {
        for (const [storyId, base64Audio] of Object.entries(data.audio)) {
          if (typeof base64Audio !== 'string') continue;
          const binaryStr = atob(base64Audio);
          const bytes = new Uint8Array(binaryStr.length);
          for (let i = 0; i < binaryStr.length; i++) {
            bytes[i] = binaryStr.charCodeAt(i);
          }
          const audioBlob = new Blob([bytes], { type: 'audio/wav' });
          await audioStorage.saveAudioBlob(storyId, audioBlob);
        }
      }

      return true;
    } catch (error) {
      console.error('导入故事和音频数据失败:', error);
      return false;
    }
  }, [importStoriesData]);

  return {
    saveStoryToLocal,
    getLocalStories,
    updateStoryAudioStatus,
    deleteStory,
    cleanupOldStories,
    getStorageStats,
    downloadStoryAsPdfWithAudio,
    exportStoriesData,
    importStoriesData,
    exportStoriesWithAudio,
    importStoriesWithAudio
  };
};
