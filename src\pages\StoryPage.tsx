import React, { useState, useMemo, useRef, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useLocation } from 'react-router-dom';
import { useStories } from '@/hooks/useStories';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2, Volume2, Spa<PERSON>les, Pause, Play } from 'lucide-react'; 
import { Slider } from '@/components/ui/slider';
import Image from '@/components/ui/Image';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'; 

// 用于存储单词时间戳的接口
interface WordTiming {
  word: string;
  start: number;
  end: number;
  charStart: number;
  charEnd: number;
}

// Helper function to parse story text into clickable and highlightable words
const ClickableText = ({ text, onWordClick, currentWordIndex = -1 }) => {
  if (!text) return null;

  // Split the text into words and non-words (punctuation, spaces, etc.)
  const parts = text.split(/(\s+|[^\w']+|\b)/);
  
  // Filter out empty strings from the split
  const filteredParts = parts.filter(part => part !== '');
  
  let wordIndex = -1;

  return (
    <p className="text-lg leading-relaxed">
      {filteredParts.map((part, index) => {
        // Check if the part is a word (contains at least one letter)
        const isWord = /[a-zA-Z]/.test(part);
        
        if (isWord) {
          wordIndex++;
          const isCurrentWord = wordIndex === currentWordIndex;
          
          return (
            <span
              key={`${index}-${wordIndex}`}
              className={`cursor-pointer transition-colors duration-200 rounded px-0.5 ${
                isCurrentWord 
                  ? 'bg-yellow-300 text-black font-medium' 
                  : 'hover:bg-yellow-200'
              }`}
              onClick={() => onWordClick(part)}
            >
              {part}
            </span>
          );
        } else {
          // This is a delimiter (space, punctuation, etc.)
          return (
            <span key={index}>
              {part}
            </span>
          );
        }
      })}
    </p>
  );
};

const StoryPage = () => {
  const { storyId } = useParams<{ storyId: string }>();
  const location = useLocation();
  const { stories, isLoading } = useStories();
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentWordIndex, setCurrentWordIndex] = useState(-1);
  const [wordTimings, setWordTimings] = useState<WordTiming[]>([]);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(0.8);

  const animationFrameRef = useRef<number>();

  const [selectedWord, setSelectedWord] = useState('');
  const [definition, setDefinition] = useState('');
  const [isDefining, setIsDefining] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  // 生成更精确的单词时间戳
  const generateWordTimings = (text: string): WordTiming[] => {
    const words = [];
    const wordRegex = /(\w+)([\s.,!?]+|$)/g;
    let match;
    let currentTime = 0;
    let charIndex = 0;
    
    // 首先按单词和标点分割
    while ((match = wordRegex.exec(text)) !== null) {
      const word = match[1];
      const punctuation = match[2] || '';
      const wordLength = word.length;
      
      if (word) {
        // 计算单词的字符位置
        const charStart = text.indexOf(word, charIndex);
        const charEnd = charStart + wordLength;
        charIndex = charEnd;
        
        // 根据单词长度和位置计算时间（这里可以进一步优化）
        const baseDuration = 0.6; // 基础持续时间
        const lengthFactor = Math.min(wordLength * 0.03, 0.4); // 长度因素
        const positionFactor = (charStart / text.length) * 0.5; // 位置因素
        const duration = baseDuration + lengthFactor + (Math.random() * 0.2 - 0.1); // 添加一些随机性
        
        const start = currentTime;
        currentTime += duration;
        
        words.push({
          word,
          start,
          end: currentTime,
          charStart,
          charEnd
        });
      }
      
      // 处理标点符号
      if (punctuation) {
        const punctuationDuration = punctuation.length * 0.2; // 标点符号的持续时间
        currentTime += punctuationDuration;
        charIndex += punctuation.length;
      }
    }
    
    return words;
  };

  const story = useMemo(() => {
    const storyFromState = location.state?.story;
    const storyFromList = stories.find(s => 
      String(s.id) === decodeURIComponent(storyId || '')
    );
    const result = storyFromState || storyFromList;
    
    if (result) {
      // 生成单词时间戳
      setWordTimings(generateWordTimings(result.story));
    }
    
    return result;
  }, [location.state, stories, storyId]);
  
  // 更新当前高亮单词和进度
  const updateCurrentWord = () => {
    if (!audioRef.current) return;
    
    const currentAudioTime = audioRef.current.currentTime;
    
    // 找到当前应该高亮的单词
    let currentWordIndex = -1;
    for (let i = 0; i < wordTimings.length; i++) {
      const timing = wordTimings[i];
      if (currentAudioTime >= timing.start && currentAudioTime < timing.end) {
        currentWordIndex = i;
        break;
      }
    }
    
    // 如果没找到，可能是音频已经播放完
    if (currentWordIndex === -1 && wordTimings.length > 0) {
      const lastWord = wordTimings[wordTimings.length - 1];
      if (currentAudioTime >= lastWord.end) {
        currentWordIndex = wordTimings.length - 1;
      }
    }
    
    setCurrentWordIndex(currentWordIndex);
    setCurrentTime(currentAudioTime);
    
    // 如果正在播放，继续更新
    if (!audioRef.current.paused) {
      animationFrameRef.current = requestAnimationFrame(updateCurrentWord);
    }
  };
  
  // 处理进度条变化
  const handleSeek = (value: number[]) => {
    if (!audioRef.current) return;
    const newTime = value[0];
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };
  
  // 处理音量变化
  const handleVolumeChange = (value: number[]) => {
    if (!audioRef.current) return;
    const newVolume = value[0];
    audioRef.current.volume = newVolume;
    setVolume(newVolume);
  };
  
  // 格式化时间（秒转 mm:ss）
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }; 
  
  // 初始化音频元素
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;
    
    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
      
      // 如果音频已经加载完成，确保时间戳已生成
      if (story) {
        const timings = generateWordTimings(story.story);
        setWordTimings(timings);
      }
    };
    
    const handlePlay = () => {
      setIsPlaying(true);
      updateCurrentWord();
    };
    
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('play', handlePlay);
    audio.volume = volume;
    
    // 如果音频已经可以播放，立即处理
    if (audio.readyState >= 2) {
      handleLoadedMetadata();
    }
    
    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('play', handlePlay);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [volume, story]);
  
  // 处理播放/暂停
  const togglePlayPause = () => {
    if (!audioRef.current) return;
    
    if (audioRef.current.paused) {
      audioRef.current.play().then(() => {
        setIsPlaying(true);
        updateCurrentWord();
      });
    } else {
      audioRef.current.pause();
      setIsPlaying(false);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    }
  };
  
  // 停止播放
  const stopPlayback = () => {
    if (!audioRef.current) return;
    
    audioRef.current.pause();
    audioRef.current.currentTime = 0;
    setIsPlaying(false);
    setCurrentWordIndex(-1);
    
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  };
  
  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  const handleWordClick = async (word) => {
    // The word is already cleaned by the ClickableText component's regex.
    if (!word) return;

    setSelectedWord(word);
    setDefinition('');
    setIsDefining(true);
    setDialogOpen(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: `Please provide a detailed explanation for the English word "${word}" for an English learner. Format the response clearly with markdown, including sections for: 1. Definition(s), 2. Phonetics (IPA), and 3. An example sentence.`,
        }),
      });
      if (!response.ok) throw new Error('Failed to get definition from AI');
      const data = await response.json();
      setDefinition(data.text);
    } catch (error) {
      setDefinition('Sorry, I could not get a definition for this word.');
      console.error(error);
    } finally {
      setIsDefining(false);
    }
  };

  if (isLoading && !story) {
    return <div className="flex justify-center items-center h-screen"><Loader2 className="h-12 w-12 animate-spin text-blue-600" /></div>;
  }

  if (!story) {
    return (
      <div className="text-center p-10">
        <h1 className="text-2xl font-bold mb-4">Story not found</h1>
        <Button asChild><Link to="/stories"><ArrowLeft className="mr-2 h-4 w-4" /> Back to Stories</Link></Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold text-gray-800">{selectedWord}</DialogTitle>
          </DialogHeader>
          <div className="mt-4 prose">
            {isDefining ? <Loader2 className="h-6 w-6 animate-spin" /> : <p className="whitespace-pre-wrap">{definition}</p>}
          </div>
        </DialogContent>
      </Dialog>

      <div className="mb-6">
        <Button asChild variant="outline"><Link to="/stories"><ArrowLeft className="mr-2 h-4 w-4" /> Back to All Stories</Link></Button>
      </div>

      <div className="bg-white p-6 sm:p-8 rounded-2xl shadow-lg">
        <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">{story.title}</h1>
        {story.storyChinese && <h2 className="text-xl text-gray-500 mb-6">{story.titleChinese || ''}</h2>}
        
        {story.imageUrl && (
          <div className="my-6 rounded-lg overflow-hidden shadow-md">
            <Image src={story.imageUrl} alt={story.title} className="w-full object-cover" />
          </div>
        )}

        {story.audioUrl && (
          <div className="my-6 p-4 bg-gray-100 rounded-lg space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center">
                <Volume2 className="mr-2"/>Listen to the Story
              </h3>
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={togglePlayPause}
                  className="flex items-center"
                  disabled={!story.audioUrl}
                >
                  {isPlaying ? (
                    <>
                      <Pause className="w-4 h-4 mr-1" /> Pause
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-1" /> Play
                    </>
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={stopPlayback}
                  className="text-red-600 hover:bg-red-50 hover:text-red-700"
                  disabled={!story.audioUrl}
                >
                  Stop
                </Button>
                <div className="flex items-center space-x-2 ml-2">
                  <Volume2 className="w-4 h-4 text-gray-500" />
                  <Slider
                    value={[volume]}
                    max={1}
                    step={0.05}
                    onValueChange={handleVolumeChange}
                    className="w-20"
                  />
                </div>
              </div>
            </div>
            
            {/* 进度条 */}
            <div className="flex items-center space-x-3">
              <span className="text-xs text-gray-500 w-10 text-right">
                {formatTime(currentTime)}
              </span>
              <div className="flex-1">
                <Slider
                  value={[currentTime]}
                  max={duration || 1}
                  step={0.1}
                  onValueChange={handleSeek}
                  disabled={!story.audioUrl}
                />
              </div>
              <span className="text-xs text-gray-500 w-10">
                {formatTime(duration)}
              </span>
            </div>
            

            
            <audio 
              ref={audioRef} 
              className="hidden" 
              src={story.audioUrl} 
              onEnded={() => {
                setIsPlaying(false);
                setCurrentWordIndex(-1);
                if (animationFrameRef.current) {
                  cancelAnimationFrame(animationFrameRef.current);
                }
              }}
            />
          </div>
        )}

        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-2xl font-semibold mb-4 border-b pb-2">English Story</h3>
            <div className="p-4 bg-white rounded-lg shadow-sm border">
              <ClickableText 
                text={story.story} 
                onWordClick={handleWordClick} 
                currentWordIndex={currentWordIndex}
              />
            </div>
          </div>
          <div>
            <h3 className="text-2xl font-semibold mb-4 border-b pb-2">中文译文</h3>
            <p className="text-lg leading-relaxed text-gray-600">{story.storyChinese || 'No translation available.'}</p>
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <Button
            variant="destructive"
            onClick={async () => {
              if (window.confirm(`Are you sure you want to delete this story?`)) {
                try {
                  const response = await fetch(`/api/stories/${story.id}`, { method: 'DELETE' });
                  if (!response.ok) throw new Error('Failed to delete story');
                  alert('Story deleted!');
                  // 跳转回故事列表页
                  window.location.href = '/stories';
                } catch (error) {
                  alert('Could not delete the story.');
                }
              }
            }}
          >
            Delete
          </Button>
        </div>
      </div>
    </div>
  );
};


export default StoryPage;
