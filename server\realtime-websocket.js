// server/realtime-websocket.js - 基于阿里云官方实时WebSocket API

import WebSocket from 'ws';

class QwenRealtimeClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.ws = null;
    this.isConnected = false;
    this.eventHandlers = {};
  }

  // 连接到阿里云官方实时WebSocket API
  async connect() {
    try {
      // 官方WebSocket端点 - 加上模型参数
      const wsUrl = `wss://dashscope.aliyuncs.com/api-ws/v1/realtime?model=qwen-omni-turbo-realtime`;
      
      console.log('[Connect] Attempting to connect to:', wsUrl);
      console.log('[Connect] API Key prefix:', this.apiKey ? this.apiKey.substring(0, 10) + '...' : 'undefined');
      
      this.ws = new WebSocket(wsUrl, [], {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });

      return new Promise((resolve, reject) => {
        // 添加超时处理
        const timeout = setTimeout(() => {
          console.log('[Connect] Connection timeout after 10 seconds');
          this.ws.close();
          reject(new Error('Connection timeout'));
        }, 10000);

        this.ws.on('open', () => {
          clearTimeout(timeout);
          console.log('[Connect] ✅ Successfully connected to Qwen-Omni Realtime API');
          this.isConnected = true;
          
          // 不立即发送session.update，等待服务器的初始消息
          this.emit('session.created', { status: 'connected' });
          resolve(true);
        });

        this.ws.on('message', (data) => {
          try {
            const event = JSON.parse(data.toString());
            console.log('[Connect] First message from server:', event);
            this.handleServerEvent(event);
          } catch (error) {
            console.error('[Connect] Error parsing WebSocket message:', error);
          }
        });

        this.ws.on('close', (code, reason) => {
          clearTimeout(timeout);
          console.log(`[Connect] ❌ WebSocket closed. Code: ${code}, Reason: ${reason}`);
          this.isConnected = false;
          this.emit('disconnected');
        });

        this.ws.on('error', (error) => {
          clearTimeout(timeout);
          console.error('[Connect] ❌ WebSocket connection error:', error.message);
          this.emit('error', { error: error.message });
          reject(error);
        });
      });
    } catch (error) {
      console.error('[Connect] ❌ Connection setup error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 处理服务器事件
  handleServerEvent(event) {
    const eventType = event.type;
    
    if (eventType !== 'response.audio.delta') {
      console.log('[Server Event]:', JSON.stringify(event, null, 2));
    }

    // 特殊处理 response.output_item.added 事件，可能包含文本内容
    if (eventType === 'response.output_item.added' && event.item) {
      console.log('[SPECIAL] Output item content:', JSON.stringify(event.item, null, 2));
      
      // 检查是否有文本内容
      if (event.item.type === 'message' && event.item.content) {
        for (const content of event.item.content) {
          if (content.type === 'text' && content.text) {
            console.log('[FOUND TEXT IN OUTPUT ITEM]:', content.text);
            // 模拟 text.delta 事件
            this.emit('response.text.delta', { delta: content.text });
          }
        }
      }
    }

    // 转发所有事件给前端
    this.emit(eventType, event);
  }

  // 发送事件到服务器
  async sendEvent(event) {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to realtime API');
    }

    event.event_id = `event_${Date.now()}`;
    console.log(`[Send Event]: type=${event.type}, event_id=${event.event_id}`);
    
    this.ws.send(JSON.stringify(event));
  }

  // 更新会话配置
  async updateSession(config) {
    const event = {
      type: "session.update",
      session: config
    };
    await this.sendEvent(event);
  }

  // 发送文本消息
  async sendTextMessage(text) {
    // 创建对话项
    const event = {
      type: "conversation.item.create",
      item: {
        type: "message",
        role: "user",
        content: [
          {
            type: "input_text",
            text: text
          }
        ]
      }
    };
    
    await this.sendEvent(event);
    
    // 创建响应
    await this.createResponse();
  }

  // 发送音频块
  async sendAudioChunk(base64Audio) {
    const event = {
      type: "input_audio_buffer.append",
      audio: base64Audio
    };
    await this.sendEvent(event);
  }

  // 提交音频缓冲区
  async commitAudio() {
    const commitEvent = {
      type: "input_audio_buffer.commit"
    };
    await this.sendEvent(commitEvent);
    
    // 创建响应
    await this.createResponse();
  }

  // 创建响应 - 按官方文档简化配置
  async createResponse() {
    const event = {
      type: "response.create",
      response: {
        modalities: ["text", "audio"]
        // 移除其他自定义配置，使用会话默认配置
      }
    };
    await this.sendEvent(event);
  }

  // 取消响应
  async cancelResponse() {
    const event = {
      type: "response.cancel"
    };
    await this.sendEvent(event);
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    console.log('Disconnected from Realtime API');
  }

  // 事件系统
  on(event, handler) {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    this.eventHandlers[event].push(handler);
  }

  emit(event, data) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error);
        }
      });
    }
  }
}

export { QwenRealtimeClient };