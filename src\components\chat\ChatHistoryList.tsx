import React from 'react';
import { MessageCircle, Trash2 } from 'lucide-react';

export interface ChatHistoryItem {
  id: string;
  title: string;
  createdAt: string;
}

interface ChatHistoryListProps {
  history: ChatHistoryItem[];
  onSelect: (id: string) => void;
  onDeleteChat: (id: string) => void;
  selectedId: string | null;
}

const ChatHistoryList: React.FC<ChatHistoryListProps> = ({ history, onSelect, onDeleteChat, selectedId }) => {
  return (
    <div className="w-64 bg-white border-r h-full flex flex-col">
      <div className="p-4 font-bold text-lg border-b flex items-center gap-2">
        <MessageCircle className="w-5 h-5 text-blue-600" />
        聊天历史
      </div>
      <div className="flex-1 overflow-y-auto">
        {history.length === 0 ? (
          <div className="text-gray-400 p-4 text-center">暂无历史</div>
        ) : (
          history.map(item => (
            <button
              key={item.id}
              className={`group flex items-center justify-between w-full text-left px-2 py-1.5 text-sm rounded-md transition-colors ${
                selectedId === item.id ? 'bg-blue-50 font-semibold' : ''
              }`}
              onClick={() => onSelect(item.id)}
            >
              <div className="flex-1 truncate pr-2">{item.title || '未命名会话'}</div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteChat(item.id);
                }}
                className="p-1 rounded-md opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 hover:bg-red-500/10 transition-opacity"
                aria-label={`Delete chat ${item.title}`}
              >
                <Trash2 size={16} />
              </button>
              <div className="text-xs text-gray-400 mt-1">{new Date(item.createdAt).toLocaleString()}</div>
            </button>
          ))
        )}
      </div>
    </div>
  );
};

export default ChatHistoryList;