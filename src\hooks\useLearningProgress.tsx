import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

export interface LearningProgress {
  id: string;
  user_id: string;
  course_type: string;
  lesson_id: string | null;
  progress_data: any;
  score: number | null;
  completed_at: string | null;
  created_at: string;
  updated_at: string;
}

export const useLearningProgress = () => {
  const [progress, setProgress] = useState<LearningProgress[]>([]);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    // 本地实现：从 localStorage 加载进度
    const savedProgress = localStorage.getItem('learning_progress');
    if (savedProgress) {
      try {
        setProgress(JSON.parse(savedProgress));
      } catch (error) {
        console.error('Failed to parse learning progress:', error);
        setProgress([]);
      }
    }
    setLoading(false);
  }, []);

  const fetchProgress = async () => {
    // 本地实现：从 localStorage 读取
    const savedProgress = localStorage.getItem('learning_progress');
    if (savedProgress) {
      try {
        setProgress(JSON.parse(savedProgress));
      } catch (error) {
        console.error('Failed to parse learning progress:', error);
        setProgress([]);
      }
    }
    setLoading(false);
  };

  const saveProgress = (newProgress: LearningProgress[]) => {
    // 本地实现：保存到 localStorage
    localStorage.setItem('learning_progress', JSON.stringify(newProgress));
    setProgress(newProgress);
  };

  const updateProgress = async (
    courseType: string,
    lessonId: string | null,
    progressData: any,
    score?: number
  ) => {
    try {
      const now = new Date().toISOString();
      const newProgressItem: LearningProgress = {
        id: `progress_${Date.now()}`,
        user_id: 'local_user',
        course_type: courseType,
        lesson_id: lessonId,
        progress_data: progressData,
        score: score || null,
        completed_at: score ? now : null,
        created_at: now,
        updated_at: now,
      };

      const existingProgress = [...progress];
      const existingIndex = existingProgress.findIndex(
        p => p.course_type === courseType && p.lesson_id === lessonId
      );

      if (existingIndex >= 0) {
        existingProgress[existingIndex] = { ...existingProgress[existingIndex], ...newProgressItem, id: existingProgress[existingIndex].id };
      } else {
        existingProgress.push(newProgressItem);
      }

      saveProgress(existingProgress);

      toast({
        title: "进度已保存",
        description: "您的学习进度已成功保存到本地"
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "保存失败",
        description: "学习进度保存失败，请重试"
      });
    }
  };

  const getProgressByCourse = (courseType: string) => {
    return progress.filter(p => p.course_type === courseType);
  };

  const getOverallStats = () => {
    const totalLessons = progress.length;
    const completedLessons = progress.filter(p => p.completed_at).length;
    const averageScore = progress.filter(p => p.score !== null).reduce((sum, p) => sum + (p.score || 0), 0) / (progress.filter(p => p.score !== null).length || 1);

    return {
      totalLessons,
      completedLessons,
      averageScore: Math.round(averageScore * 100) / 100,
      completionRate: totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0
    };
  };

  return {
    progress,
    loading,
    fetchProgress,
    updateProgress,
    getProgressByCourse,
    getOverallStats
  };
};
