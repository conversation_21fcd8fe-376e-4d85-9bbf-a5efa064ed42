// server/qwen-omni-realtime.js - 基于阿里云官方Qwen-Omni-Realtime WebSocket API
// 根据官方文档：Qwen-Omni-Realtime系列模型仅支持 OpenAI 兼容方式调用

import WebSocket from 'ws';
import { HttpsProxyAgent } from 'https-proxy-agent';

class QwenOmniRealtimeClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.ws = null;
    this.isConnected = false;
    this.eventHandlers = {};

    // 设置代理
    this.agent = null;
    if (process.env.HTTPS_PROXY) {
      this.agent = new HttpsProxyAgent(process.env.HTTPS_PROXY);
      console.log('[Proxy] Using HTTPS proxy:', process.env.HTTPS_PROXY);
    }
  }

  // 连接到Qwen-Omni-Realtime WebSocket API
  async connect() {
    try {
      console.log('[Connect] Connecting to Qwen-Omni-Realtime WebSocket...');
      console.log('[Connect] API Key prefix:', this.apiKey ? this.apiKey.substring(0, 10) + '...' : 'undefined');

      // 官方WebSocket端点
      const wsUrl = 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime?model=qwen-omni-turbo-realtime';

      const wsOptions = {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      };

      // 如果有代理，添加代理配置
      if (this.agent) {
        wsOptions.agent = this.agent;
      }

      this.ws = new WebSocket(wsUrl, wsOptions);

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          console.log('[Connect] Connection timeout after 15 seconds');
          this.ws.close();
          reject(new Error('Connection timeout'));
        }, 15000);

        this.ws.on('open', () => {
          clearTimeout(timeout);
          console.log('[Connect] ✅ Successfully connected to Qwen-Omni-Realtime');
          this.isConnected = true;
          this.emit('session.created', { status: 'connected' });
          resolve(true);
        });

        this.ws.on('message', (data) => {
          try {
            const event = JSON.parse(data.toString());
            this.handleServerEvent(event);
          } catch (error) {
            console.error('[Connect] Error parsing WebSocket message:', error);
          }
        });

        this.ws.on('error', (error) => {
          clearTimeout(timeout);
          console.error('[Connect] ❌ WebSocket connection error:', error);
          this.isConnected = false;
          this.emit('error', { error: error.message });
          reject(error);
        });

        this.ws.on('close', (code, reason) => {
          clearTimeout(timeout);
          console.log(`[Connect] WebSocket connection closed. Code: ${code}, Reason: ${reason}`);
          this.isConnected = false;
          this.emit('connection.closed', { code, reason: reason.toString() });
        });
      });
    } catch (error) {
      console.error('[Connect] ❌ Failed to create WebSocket connection:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 处理服务器事件
  handleServerEvent(event) {
    const eventType = event.type;

    if (eventType !== 'response.audio.delta') {
      console.log('[Server Event]:', JSON.stringify(event, null, 2));
    }

    // 转发所有事件给前端
    this.emit(eventType, event);
  }

  // 事件处理器
  on(eventType, handler) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  emit(eventType, data) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => handler(data));
    }
  }

  // 发送事件到服务器
  async sendEvent(event) {
    if (!this.isConnected || !this.ws) {
      throw new Error('Not connected to realtime API');
    }

    event.event_id = `event_${Date.now()}`;
    console.log(`[Send Event]: type=${event.type}, event_id=${event.event_id}`);

    this.ws.send(JSON.stringify(event));
  }

  // 更新会话配置
  async updateSession(config) {
    const event = {
      type: "session.update",
      session: config
    };
    await this.sendEvent(event);
  }

  // 发送文本消息
  async sendTextMessage(text) {
    if (!this.isConnected) {
      throw new Error('Not connected to realtime API');
    }

    try {
      console.log('[Send Text] Sending message:', text);

      // 创建对话项
      const createItemEvent = {
        type: "conversation.item.create",
        item: {
          type: "message",
          role: "user",
          content: [
            {
              type: "input_text",
              text: text
            }
          ]
        }
      };

      await this.sendEvent(createItemEvent);

      // 创建响应
      await this.createResponse();

    } catch (error) {
      console.error('[Send Text] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 发送多模态消息（文本+图片）
  async sendMultimodalMessage(text, imageBase64) {
    if (!this.isConnected) {
      throw new Error('Not connected to realtime API');
    }

    try {
      console.log('[Send Multimodal] Sending message with image');

      const content = [
        {
          type: "input_text",
          text: text
        }
      ];

      if (imageBase64) {
        content.push({
          type: "input_image",
          image: `data:image/jpeg;base64,${imageBase64}`
        });
      }

      // 创建对话项
      const createItemEvent = {
        type: "conversation.item.create",
        item: {
          type: "message",
          role: "user",
          content: content
        }
      };

      await this.sendEvent(createItemEvent);

      // 创建响应
      await this.createResponse();

    } catch (error) {
      console.error('[Send Multimodal] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 创建响应
  async createResponse() {
    const event = {
      type: "response.create",
      response: {
        modalities: ["text", "audio"]
      }
    };
    await this.sendEvent(event);
  }

  // 发送音频块
  async sendAudioChunk(base64Audio) {
    const event = {
      type: "input_audio_buffer.append",
      audio: base64Audio
    };
    await this.sendEvent(event);
  }

  // 提交音频缓冲区
  async commitAudio() {
    const commitEvent = {
      type: "input_audio_buffer.commit"
    };
    await this.sendEvent(commitEvent);

    // 创建响应
    await this.createResponse();
  }

  // 取消响应
  async cancelResponse() {
    const event = {
      type: "response.cancel"
    };
    await this.sendEvent(event);
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    console.log('[Disconnect] Disconnected from Qwen-Omni-Realtime API');
  }
}

export { QwenOmniRealtimeClient as QwenRealtimeClient };
