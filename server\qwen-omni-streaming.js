// server/qwen-omni-streaming.js - 基于阿里云官方OpenAI兼容流式API
// 根据官方文档：https://www.alibabacloud.com/help/en/model-studio/qwen-omni

import fetch from 'node-fetch';

class QwenOmniStreamingClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.isConnected = false;
    this.eventHandlers = {};
    this.currentStream = null;
    this.audioBuffer = '';
    this.textBuffer = '';
    this.baseUrl = 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1';
  }

  // 模拟连接 - 直接标记为已连接，不进行预先测试
  async connect() {
    try {
      console.log('[Connect] Initializing Qwen-Omni streaming client...');
      console.log('[Connect] API Key prefix:', this.apiKey ? this.apiKey.substring(0, 10) + '...' : 'undefined');

      // 直接标记为已连接，API测试将在实际请求时进行
      console.log('[Connect] ✅ Qwen-Omni streaming client ready');
      this.isConnected = true;

      // 发送连接成功事件
      this.emit('session.created', { status: 'connected' });
      return true;
    } catch (error) {
      console.error('[Connect] ❌ Connection setup error:', error);
      this.isConnected = false;
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 事件处理器
  on(eventType, handler) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  emit(eventType, data) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => handler(data));
    }
  }

  // 发送文本消息
  async sendTextMessage(text) {
    if (!this.isConnected) {
      throw new Error('Not connected to API');
    }

    try {
      console.log('[Send Text] Sending message:', text);
      
      const requestBody = {
        model: "qwen2.5-omni-7b",
        messages: [
          {
            role: "user",
            content: text
          }
        ],
        stream: true,
        stream_options: {
          include_usage: true
        },
        modalities: ["text", "audio"],
        audio: {
          voice: "Chelsie",
          format: "wav"
        }
      };

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      // 处理流式响应
      await this.handleStreamResponse(response);
      
    } catch (error) {
      console.error('[Send Text] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 发送多模态消息（文本+图片）
  async sendMultimodalMessage(text, imageBase64) {
    if (!this.isConnected) {
      throw new Error('Not connected to API');
    }

    try {
      console.log('[Send Multimodal] Sending message with image');
      
      const content = [
        {
          type: "text",
          text: text
        }
      ];

      if (imageBase64) {
        content.push({
          type: "image_url",
          image_url: {
            url: `data:image/jpeg;base64,${imageBase64}`
          }
        });
      }

      const requestBody = {
        model: "qwen2.5-omni-7b",
        messages: [
          {
            role: "user",
            content: content
          }
        ],
        stream: true,
        stream_options: {
          include_usage: true
        },
        modalities: ["text", "audio"],
        audio: {
          voice: "Chelsie",
          format: "wav"
        }
      };

      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      // 处理流式响应
      await this.handleStreamResponse(response);
      
    } catch (error) {
      console.error('[Send Multimodal] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 处理流式响应
  async handleStreamResponse(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    this.audioBuffer = '';
    this.textBuffer = '';
    
    // 发送响应开始事件
    this.emit('response.created', {});
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              // 发送完成事件
              this.emit('response.done', {});
              
              // 如果有音频数据，发送完整的音频
              if (this.audioBuffer) {
                this.emit('response.audio.done', { 
                  audio: this.audioBuffer 
                });
              }
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              await this.processStreamChunk(parsed);
            } catch (parseError) {
              console.error('[Stream] Parse error:', parseError, 'Data:', data);
            }
          }
        }
      }
    } catch (error) {
      console.error('[Stream] Reading error:', error);
      this.emit('error', { error: error.message });
    }
  }

  // 处理流式数据块
  async processStreamChunk(chunk) {
    if (chunk.choices && chunk.choices.length > 0) {
      const choice = chunk.choices[0];
      const delta = choice.delta;
      
      // 处理文本内容
      if (delta.content) {
        this.textBuffer += delta.content;
        this.emit('response.text.delta', { 
          delta: delta.content 
        });
      }
      
      // 处理音频内容
      if (delta.audio && delta.audio.data) {
        this.audioBuffer += delta.audio.data;
        this.emit('response.audio.delta', { 
          delta: delta.audio.data 
        });
      }
    }
    
    // 处理使用统计
    if (chunk.usage) {
      this.emit('response.usage', chunk.usage);
    }
  }

  // 断开连接
  disconnect() {
    this.isConnected = false;
    if (this.currentStream) {
      this.currentStream.cancel();
      this.currentStream = null;
    }
    console.log('[Disconnect] Disconnected from Qwen-Omni API');
  }

  // 更新会话配置（兼容性方法）
  async updateSession(config) {
    console.log('[Session] Session config updated:', config);
    this.emit('session.updated', config);
  }

  // 发送事件（兼容性方法）
  async sendEvent(event) {
    console.log('[Event] Received event:', event.type);
    
    switch (event.type) {
      case 'conversation.item.create':
        if (event.item && event.item.content) {
          const textContent = event.item.content.find(c => c.type === 'input_text');
          if (textContent) {
            await this.sendTextMessage(textContent.text);
          }
        }
        break;
      case 'response.create':
        // 这个在sendTextMessage中已经处理了
        break;
      default:
        console.log('[Event] Unhandled event type:', event.type);
    }
  }
}

export { QwenOmniStreamingClient as QwenRealtimeClient };
