// server/qwen-omni-realtime.js - 基于阿里云官方Qwen-Omni-Realtime WebSocket API
// 严格按照官方文档实现：https://help.aliyun.com/document_detail/2880812.html

import WebSocket from 'ws';
import { HttpsProxyAgent } from 'https-proxy-agent';

class QwenOmniRealtimeClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.ws = null;
    this.isConnected = false;
    this.eventHandlers = {};

    // 官方文档指定的配置
    this.baseUrl = 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime';
    this.model = 'qwen-omni-turbo-realtime';
    this.voice = 'Chelsie'; // 支持的音色：Chelsie、Serena、Ethan、Cherry

    // 当前状态
    this._currentResponseId = null;
    this._isResponding = false;
  }

  // 连接到Qwen-Omni-Realtime WebSocket API - 严格按照官方文档
  async connect() {
    try {
      console.log('[Connect] Connecting to Qwen-Omni-Realtime WebSocket...');
      console.log('[Connect] API Key prefix:', this.apiKey ? this.apiKey.substring(0, 10) + '...' : 'undefined');

      // 官方文档指定的WebSocket地址和查询参数
      const wsUrl = `${this.baseUrl}?model=${this.model}`;
      console.log('[Connect] WebSocket URL:', wsUrl);

      const wsOptions = {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      };

      // 检查代理配置 - 使用正确的WebSocket代理设置
      if (process.env.HTTPS_PROXY) {
        console.log('[Connect] Proxy configured:', process.env.HTTPS_PROXY);
        console.log('[Connect] Setting up WebSocket proxy...');
        try {
          const agent = new HttpsProxyAgent(process.env.HTTPS_PROXY);
          wsOptions.agent = agent;
          console.log('[Connect] WebSocket proxy agent configured');
        } catch (proxyError) {
          console.error('[Connect] Proxy setup failed:', proxyError);
          console.log('[Connect] Attempting connection without proxy...');
        }
      } else {
        console.log('[Connect] No proxy configured, connecting directly...');
      }

      this.ws = new WebSocket(wsUrl, wsOptions);

      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          console.log('[Connect] Connection timeout after 20 seconds');
          if (this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close();
          }
          reject(new Error('Connection timeout'));
        }, 20000);

        this.ws.on('open', () => {
          clearTimeout(timeout);
          console.log('[Connect] ✅ WebSocket connection established');
          this.isConnected = true;

          // 按照官方文档，连接成功后立即设置会话配置
          this.setupSession();

          this.emit('session.created', { status: 'connected' });
          resolve(true);
        });

        this.ws.on('message', (data) => {
          try {
            const event = JSON.parse(data.toString());
            this.handleServerEvent(event);
          } catch (error) {
            console.error('[Connect] Error parsing WebSocket message:', error);
            console.log('[Connect] Raw message:', data.toString());
          }
        });

        this.ws.on('error', (error) => {
          clearTimeout(timeout);
          console.error('[Connect] ❌ WebSocket connection error:', error);
          this.isConnected = false;
          this.emit('error', { error: error.message });
          reject(error);
        });

        this.ws.on('close', (code, reason) => {
          clearTimeout(timeout);
          console.log(`[Connect] WebSocket connection closed. Code: ${code}, Reason: ${reason}`);
          this.isConnected = false;
          this.emit('connection.closed', { code, reason: reason.toString() });
        });
      });
    } catch (error) {
      console.error('[Connect] ❌ Failed to create WebSocket connection:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 设置会话配置 - 使用Manual模式便于测试
  async setupSession() {
    try {
      const sessionConfig = {
        type: "session.update",
        session: {
          modalities: ["text", "audio"],
          voice: this.voice,
          input_audio_format: "pcm16",
          output_audio_format: "pcm16",
          input_audio_transcription: {
            model: "gummy-realtime-v1"
          },
          turn_detection: null  // Manual模式，客户端控制
        }
      };

      console.log('[Session] Setting up Manual mode session...');
      await this.sendEvent(sessionConfig);
    } catch (error) {
      console.error('[Session] Failed to setup session:', error);
    }
  }

  // 处理服务器事件 - 按照官方文档的事件流程
  handleServerEvent(event) {
    const eventType = event.type;

    // 只打印非音频增量事件，避免日志过多
    if (eventType !== 'response.audio.delta') {
      console.log(`[Server Event] ${eventType}:`, JSON.stringify(event, null, 2));
    } else {
      console.log(`[Server Event] ${eventType}: audio chunk received`);
    }

    // 按照官方文档处理特定事件
    switch (eventType) {
      case 'session.created':
        console.log('[Session] Session created successfully');
        break;
      case 'session.updated':
        console.log('[Session] Session updated successfully');
        break;
      case 'error':
        console.error('[Server Error]:', event.error);
        break;
      case 'response.created':
        this._currentResponseId = event.response?.id;
        this._isResponding = true;
        console.log('[Response] Response created, ID:', this._currentResponseId);
        break;
      case 'response.done':
        this._isResponding = false;
        this._currentResponseId = null;
        console.log('[Response] Response completed');
        break;
      case 'input_audio_buffer.speech_started':
        console.log('[Audio] Speech detected - user started speaking');
        if (this._isResponding) {
          console.log('[Audio] Handling interruption - canceling current response');
          this.cancelResponse();
        }
        break;
      case 'input_audio_buffer.speech_stopped':
        console.log('[Audio] Speech ended - user stopped speaking');
        break;
      case 'response.text.delta':
        // 文本增量
        this.emit('response.text.delta', { delta: event.delta });
        break;
      case 'response.audio.delta':
        // 音频增量
        this.emit('response.audio.delta', { delta: event.delta });
        break;
      case 'response.audio_transcript.delta':
        // 音频转录增量
        console.log('[Transcript] Audio transcript delta:', event.delta);
        break;
      case 'conversation.item.input_audio_transcription.completed':
        // 输入音频转录完成
        console.log('[Transcript] Input transcription completed:', event.transcript);
        break;
    }

    // 转发所有事件给前端
    this.emit(eventType, event);
  }

  // 事件处理器
  on(eventType, handler) {
    if (!this.eventHandlers[eventType]) {
      this.eventHandlers[eventType] = [];
    }
    this.eventHandlers[eventType].push(handler);
  }

  emit(eventType, data) {
    if (this.eventHandlers[eventType]) {
      this.eventHandlers[eventType].forEach(handler => handler(data));
    }
  }

  // 发送事件到服务器 - 按照官方文档格式
  async sendEvent(event) {
    if (!this.isConnected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket not connected or not ready');
    }

    // 按照官方文档，添加事件ID
    event.event_id = `event_${Date.now()}`;

    const eventJson = JSON.stringify(event);
    console.log(`[Send Event] ${event.type}:`, eventJson);

    this.ws.send(eventJson);
  }

  // 更新会话配置
  async updateSession(config) {
    const event = {
      type: "session.update",
      session: config
    };
    await this.sendEvent(event);
  }

  // 发送文本消息 - Manual模式
  async sendTextMessage(text) {
    if (!this.isConnected) {
      throw new Error('Not connected to realtime API');
    }

    try {
      console.log('[Send Text] Sending text message:', text);

      // 按照官方文档，Manual模式下需要手动创建对话项和响应
      const createItemEvent = {
        type: "conversation.item.create",
        item: {
          type: "message",
          role: "user",
          content: [
            {
              type: "text",
              text: text
            }
          ]
        }
      };

      await this.sendEvent(createItemEvent);

      // Manual模式下需要手动触发响应创建
      await this.createResponse();

    } catch (error) {
      console.error('[Send Text] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 发送多模态消息（文本+图片）
  async sendMultimodalMessage(text, imageBase64) {
    if (!this.isConnected) {
      throw new Error('Not connected to realtime API');
    }

    try {
      console.log('[Send Multimodal] Sending message with image');

      const content = [
        {
          type: "input_text",
          text: text
        }
      ];

      if (imageBase64) {
        content.push({
          type: "input_image",
          image: `data:image/jpeg;base64,${imageBase64}`
        });
      }

      // 创建对话项
      const createItemEvent = {
        type: "conversation.item.create",
        item: {
          type: "message",
          role: "user",
          content: content
        }
      };

      await this.sendEvent(createItemEvent);

      // 创建响应
      await this.createResponse();

    } catch (error) {
      console.error('[Send Multimodal] Error:', error);
      this.emit('error', { error: error.message });
      throw error;
    }
  }

  // 创建响应 - 按照官方文档格式
  async createResponse() {
    const event = {
      type: "response.create",
      response: {
        instructions: "You are a helpful English learning assistant.",
        modalities: ["text", "audio"]
      }
    };
    console.log('[Response] Creating response...');
    await this.sendEvent(event);
  }

  // 发送音频块
  async sendAudioChunk(base64Audio) {
    const event = {
      type: "input_audio_buffer.append",
      audio: base64Audio
    };
    await this.sendEvent(event);
  }

  // 提交音频缓冲区
  async commitAudio() {
    const commitEvent = {
      type: "input_audio_buffer.commit"
    };
    await this.sendEvent(commitEvent);

    // 创建响应
    await this.createResponse();
  }

  // 取消响应
  async cancelResponse() {
    const event = {
      type: "response.cancel"
    };
    await this.sendEvent(event);
  }

  // 断开连接
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
    console.log('[Disconnect] Disconnected from Qwen-Omni-Realtime API');
  }
}

export { QwenOmniRealtimeClient as QwenRealtimeClient };
