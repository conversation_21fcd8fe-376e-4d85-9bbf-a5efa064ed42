import { useState, useEffect, useRef, useCallback } from 'react';

interface UseAudioPlayerProps {
  onEnd?: () => void;
}

export const useAudioPlayer = ({ onEnd }: UseAudioPlayerProps = {}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    const audio = new Audio();
    audio.preload = 'auto';
    audioRef.current = audio;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => {
      setIsPlaying(false);
      onEnd?.();
    };
    const handleWaiting = () => setIsLoading(true);
    const handlePlaying = () => {
        setIsLoading(false);
        setIsPlaying(true);
    };
    const handleCanPlay = () => setIsLoading(false);
    const handleError = () => {
      setError('音频播放时发生错误。');
      setIsLoading(false);
      setIsPlaying(false);
    };

    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('waiting', handleWaiting);
    audio.addEventListener('playing', handlePlaying);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('waiting', handleWaiting);
      audio.removeEventListener('playing', handlePlaying);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('error', handleError);
      audio.pause();
    };
  }, [onEnd]);

  const play = useCallback(async (audioSrc: string) => {
    if (!audioRef.current) return;
    
    // If it's already playing the same source, do nothing.
    if (isPlaying && audioRef.current.src === audioSrc) return;

    setIsLoading(true);
    setError(null);

    // Set the new source if it's different
    if (audioRef.current.src !== audioSrc) {
        audioRef.current.src = audioSrc;
        audioRef.current.load();
    }

    try {
      await audioRef.current.play();
    } catch (e) {
      console.error("Play failed:", e);
      setError("播放失败。您的浏览器可能阻止了自动播放，请尝试手动点击播放。 ");
      setIsLoading(false);
    }
  }, [isPlaying]);

  const pause = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
    }
  }, []);

  return { isPlaying, isLoading, error, play, pause };
};
