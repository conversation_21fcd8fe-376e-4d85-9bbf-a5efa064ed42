
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const googleAIApiKey = Deno.env.get('GOOGLE_AI_API_KEY');
const openRouterApiKey = Deno.env.get('OPENROUTER_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { message, image, conversationHistory, model = 'gemini-2.0-flash-exp' } = await req.json();

    console.log(`Using model: ${model}`);

    let response;
    let aiResponse;

    if (model.startsWith('gemini') && !model.includes('/')) {
      // Google AI (Gemini) integration
      if (!googleAIApiKey) {
        throw new Error('Google AI API key not configured');
      }

      const systemInstruction = {
        role: 'user',
        parts: [{ text: '你是一个专业的英语学习助手。请用中文回答问题，帮助用户学习英语。你可以解释语法、词汇、发音，提供例句，纠正错误，并给出学习建议。请保持回答简洁明了。' }]
      };
      const modelInstruction = {
          role: 'model',
          parts: [{ text: '好的，我明白了。我会尽力帮助你学习英语。' }]
      };

      // Note: This assumes history messages do not contain images.
      const contents = (conversationHistory || []).map(msg => ({
        role: msg.role === 'assistant' ? 'model' : 'user',
        parts: [{ text: msg.content }]
      }));

      const currentUserParts: Array<{ text: string } | { inlineData: { mimeType: string; data: string; } }> = [];
      // The text part must come first for Gemini
      if (message) {
        currentUserParts.push({ text: message });
      }
      if (image) {
        const match = image.match(/^data:(image\/\w+);base64,(.*)$/);
        if (match) {
          currentUserParts.push({
            inlineData: {
              mimeType: match[1],
              data: match[2]
            }
          });
        } else {
          console.warn("Invalid image data format received.");
        }
      }
      
      const finalContents = [systemInstruction, modelInstruction, ...contents, { role: 'user', parts: currentUserParts }];

      response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${googleAIApiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: finalContents,
          generationConfig: {
            temperature: 0.7,
            maxOutputTokens: 256,
          }
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Google AI API error: ${error}`);
      }

      const data = await response.json();
      aiResponse = data.candidates[0].content.parts[0].text;

    } else {
      // OpenRouter integration
      if (!openRouterApiKey) {
        throw new Error('OpenRouter API key not configured');
      }

      const messages = [
        {
          role: 'system',
          content: '你是一个专业的英语学习助手。请用中文回答问题，帮助用户学习英语。你可以解释语法、词汇、发音，提供例句，纠正错误，并给出学习建议。请保持回答简洁明了。'
        },
        // Note: This assumes history messages do not contain images.
        ...(conversationHistory || []).map(msg => ({ role: msg.role, content: msg.content })),
      ];

      const userMessageContent: Array<{ type: 'text'; text: string } | { type: 'image_url'; image_url: { url: string } }> = [];
      if (message) {
        userMessageContent.push({ type: 'text', text: message });
      }
      if (image) {
        userMessageContent.push({
          type: 'image_url',
          image_url: {
            url: image // OpenRouter accepts the full data URI
          }
        });
      }
      
      messages.push({ role: 'user', content: userMessageContent });

      response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://lovable.dev',
          'X-Title': 'English Learning Assistant'
        },
        body: JSON.stringify({
          model: model,
          messages: messages,
          max_tokens: 256,
          temperature: 0.7,
        }),
      });

      if (!response.ok) {
        const error = await response.text();
        throw new Error(`OpenRouter API error: ${error}`);
      }

      const data = await response.json();
      aiResponse = data.choices[0].message.content;
    }

    return new Response(JSON.stringify({ 
      response: aiResponse,
      model: model
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error in chat-ai function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
