// 测试代理连接
import fetch from 'node-fetch';
import { HttpsProxyAgent } from 'https-proxy-agent';
import dotenv from 'dotenv';

dotenv.config();

async function testProxy() {
  const proxyUrl = process.env.HTTPS_PROXY;
  
  if (!proxyUrl) {
    console.log('❌ No proxy configured');
    return;
  }
  
  console.log('Testing proxy:', proxyUrl);
  
  try {
    const agent = new HttpsProxyAgent(proxyUrl);
    
    // 测试简单的HTTP请求
    console.log('Testing HTTP request through proxy...');
    const response = await fetch('https://httpbin.org/ip', {
      agent: agent,
      timeout: 10000
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Proxy working! Response:', result);
    } else {
      console.log('❌ Proxy request failed:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('❌ Proxy test failed:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  }
}

testProxy();
