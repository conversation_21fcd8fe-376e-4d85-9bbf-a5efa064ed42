// 测试基本的阿里云API连接
import fetch from 'node-fetch';
import { HttpsProxyAgent } from 'https-proxy-agent';
import dotenv from 'dotenv';

dotenv.config();

async function testBasicConnection() {
  const apiKey = process.env.DASHSCOPE_API_KEY;
  
  console.log('Testing basic connection to DashScope...');
  console.log('API Key prefix:', apiKey ? apiKey.substring(0, 10) + '...' : 'undefined');
  
  // 测试不同的端点
  const endpoints = [
    'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'
  ];
  
  for (const endpoint of endpoints) {
    console.log(`\n--- Testing endpoint: ${endpoint} ---`);
    
    const requestBody = {
      model: "qwen-turbo",
      input: {
        messages: [
          {
            role: "user",
            content: "Hello"
          }
        ]
      },
      parameters: {
        temperature: 0.7,
        max_tokens: 50
      }
    };
    
    // 如果是OpenAI兼容端点，使用不同的格式
    if (endpoint.includes('compatible-mode')) {
      requestBody.messages = requestBody.input.messages;
      delete requestBody.input;
      delete requestBody.parameters;
      requestBody.max_tokens = 50;
      requestBody.temperature = 0.7;
    }
    
    const fetchOptions = {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody),
      timeout: 10000
    };
    
    // 尝试使用代理
    if (process.env.HTTPS_PROXY) {
      try {
        fetchOptions.agent = new HttpsProxyAgent(process.env.HTTPS_PROXY);
        console.log('Using proxy:', process.env.HTTPS_PROXY);
      } catch (error) {
        console.error('Proxy setup error:', error);
      }
    }
    
    try {
      console.log('Sending request...');
      const response = await fetch(endpoint, fetchOptions);
      
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (response.ok) {
        const result = await response.json();
        console.log('✅ Success! Response:', JSON.stringify(result, null, 2));
      } else {
        const errorText = await response.text();
        console.log('❌ Error response:', errorText);
      }
    } catch (error) {
      console.error('❌ Request failed:', error.message);
      if (error.code) {
        console.error('Error code:', error.code);
      }
    }
  }
}

testBasicConnection().catch(console.error);
