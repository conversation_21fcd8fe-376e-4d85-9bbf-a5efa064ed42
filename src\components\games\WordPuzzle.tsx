import React, { useState, useEffect, useRef } from 'react';
import { wordPuzzleData, Puzzle } from '@/data/wordPuzzleData';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Lightbulb, CheckCircle, XCircle, Sparkles, Loader2, ArrowLeft } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { supabase } from '@/integrations/supabase/client';
import { cn } from '@/lib/utils';

interface WordPuzzleProps {
  puzzle?: Puzzle; // For a single, specific puzzle
  puzzles?: Puzzle[]; // For a series of puzzles (e.g., AI generated)
  onComplete?: () => void; // Callback for single puzzle completion
  onCompleteAll?: () => void; // Callback for when all puzzles in a series are completed
  isAIGenerated?: boolean;
  onBack?: () => void; // To return to a previous view
}

const WordPuzzle: React.FC<WordPuzzleProps> = ({
  puzzle,
  puzzles,
  onComplete,
  onCompleteAll,
  isAIGenerated = false,
  onBack,
}) => {
  // Determine mode based on props
  const isDynamicListMode = puzzles && puzzles.length > 0;
  const isSinglePuzzleMode = !!puzzle && !isDynamicListMode;

  // State for puzzle progression
  const [currentPuzzleIndex, setCurrentPuzzleIndex] = useState(0);

  // State for static data progression (fallback when no props are passed)
  const [currentLevelIndex, setCurrentLevelIndex] = useState(0);
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  // Common state
  const [inputValue, setInputValue] = useState('');
  const [feedback, setFeedback] = useState<'correct' | 'incorrect' | null>(null);
  const [score, setScore] = useState(0);
  const [showHint, setShowHint] = useState(false);
  const [aiExplanation, setAiExplanation] = useState('');
  const [isAiLoading, setIsAiLoading] = useState(false);

  // --- Sound Effects ---
  const soundsRef = useRef({
    keyboard: new Audio('/assets/sounds/keyboard-sound.mp3'),
    correct: new Audio('/assets/sounds/correct-sound.mp3'),
    incorrect: new Audio('/assets/sounds/incorrect-sound.mp3'),
  });

  useEffect(() => {
    // Preload audio files
    Object.values(soundsRef.current).forEach(sound => {
      sound.load();
    });
  }, []);

  const playSound = (sound: keyof typeof soundsRef.current) => {
    const audio = soundsRef.current[sound];
    audio.pause();
    audio.currentTime = 0;
    audio.play().catch(err => {
      if (err.name !== 'AbortError') {
        console.error(`Error playing sound: ${sound}`, err);
      }
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    playSound('keyboard');
  };
  // ---------------------

  // A unified way to get the current puzzle data, regardless of mode.
  const getCurrentPuzzleData = () => {
    if (isDynamicListMode) {
      return puzzles[currentPuzzleIndex];
    }
    if (isSinglePuzzleMode) {
      return puzzle;
    }
    // Fallback to static data from wordPuzzleData.ts
    const level = wordPuzzleData[currentLevelIndex];
    const wordData = level.words[currentWordIndex];
    return {
      id: `${currentLevelIndex}-${currentWordIndex}`,
      word: wordData.word,
      hint: wordData.hint,
      image: wordData.imageUrl,
      level: level.title,
    };
  };

  const currentPuzzle = getCurrentPuzzleData();
  
  const title = isDynamicListMode 
    ? (isAIGenerated ? `AI Challenge (${currentPuzzleIndex + 1}/${puzzles.length})` : `Level ${currentPuzzle.level}`)
    : currentPuzzle.level;
  
  const progress = isDynamicListMode
    ? ((currentPuzzleIndex + 1) / puzzles.length) * 100
    : isSinglePuzzleMode
    ? (feedback === 'correct' ? 100 : 0)
    : ((currentWordIndex + 1) / wordPuzzleData[currentLevelIndex].words.length) * 100;

  const handleNext = () => {
    setFeedback(null);
    setInputValue('');
    setShowHint(false);
    setAiExplanation('');

    if (isDynamicListMode) {
      if (currentPuzzleIndex < puzzles.length - 1) {
        setCurrentPuzzleIndex(currentPuzzleIndex + 1);
      } else {
        if (onCompleteAll) onCompleteAll();
      }
    } else if (isSinglePuzzleMode) {
      if (onComplete) onComplete();
    } else {
      // Static mode progression
      const currentLevel = wordPuzzleData[currentLevelIndex];
      if (currentWordIndex < currentLevel.words.length - 1) {
        setCurrentWordIndex(currentWordIndex + 1);
      } else {
        if (currentLevelIndex < wordPuzzleData.length - 1) {
          setCurrentLevelIndex(currentLevelIndex + 1);
          setCurrentWordIndex(0);
        } else {
          alert(`Congratulations! You have completed all levels with a score of ${score}`);
          if (onBack) onBack();
        }
      }
    }
  };

  const handleCheckAnswer = () => {
    if (inputValue.trim().toLowerCase() === currentPuzzle.word.toLowerCase()) {
      setFeedback('correct');
      setScore(score + 10);
      playSound('correct');
      setTimeout(handleNext, 1500);
    } else {
      setFeedback('incorrect');
      playSound('incorrect');
    }
  };

  const getAIExplanation = async () => {
    setIsAiLoading(true);
    setAiExplanation('');
    try {
      const { data, error } = await supabase.functions.invoke('chat-ai', {
        body: {
          message: `I'm playing a word puzzle. The hint is "${currentPuzzle.hint}". I guessed "${inputValue}" but it was wrong. The correct answer is "${currentPuzzle.word}". Please explain the meaning of the word "${currentPuzzle.word}" and why my guess was incorrect, in a simple and encouraging way.`,
          conversationHistory: [],
          model: 'gemini-2.0-flash-exp',
        },
      });
      if (error) throw error;
      setAiExplanation(data.response);
    } catch (err: any) {
      setAiExplanation('Sorry, I could not get an explanation at this time.');
    } finally {
      setIsAiLoading(false);
    }
  };

  return (
    <Card className={cn(
        "w-full max-w-2xl mx-auto flex flex-col transition-all duration-300",
        feedback === 'correct' && "border-green-500"
    )}>
      <CardHeader>
        <div className="flex justify-between items-center mb-2">
            {(onBack || isAIGenerated) && (
              <Button variant="outline" size="sm" onClick={onBack ?? onCompleteAll}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  {isAIGenerated ? 'End AI Challenge' : 'Back to Games'}
              </Button>
            )}
            <div className={cn("text-lg font-bold text-yellow-500", !(onBack || isAIGenerated) && "w-full text-right")}>Score: {score}</div>
        </div>
        <div className="text-center">
            <CardTitle>{title}</CardTitle>
            {!isAIGenerated && <CardDescription>Guess the word based on the image and hint.</CardDescription>}
        </div>
        <Progress value={progress} className="mt-4" />
      </CardHeader>
      <CardContent className="text-center flex-grow flex flex-col">
        <div className="flex-grow flex items-center justify-center">
            <img 
                src={currentPuzzle.image} 
                alt="Word hint" 
                className="mx-auto mb-4 rounded-lg w-full max-w-sm h-64 object-contain bg-gray-100 dark:bg-gray-800" 
                // Add a key to force re-render on word change
                key={currentPuzzle.id}
            />
        </div>
        
        <div className="mt-auto">
            {feedback === 'correct' && (
              <div className="mb-4 flex items-center justify-center text-green-500 animate-pulse">
                <CheckCircle className="w-8 h-8 mr-2" />
                <p className="text-xl font-semibold">Correct!</p>
              </div>
            )}

            {feedback === 'incorrect' && (
              <div className="mb-4 flex items-center justify-center text-red-500">
                  <XCircle className="w-6 h-6 mr-2" />
                  <p className="text-lg font-semibold">Not quite, try again!</p>
              </div>
            )}

            <div className="flex w-full max-w-sm items-center space-x-2 mx-auto">
              <Input 
                type="text" 
                placeholder="Type your guess..." 
                value={inputValue}
                onChange={handleInputChange}
                onKeyPress={(e) => e.key === 'Enter' && handleCheckAnswer()}
                className="text-center text-lg focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-shadow duration-300"
                disabled={feedback === 'correct'}
              />
              <Button onClick={handleCheckAnswer} disabled={feedback === 'correct'}>Check</Button>
            </div>

            <div className="mt-4 flex justify-center gap-2">
              <Button variant="outline" onClick={() => setShowHint(true)} disabled={showHint}>
                <Lightbulb className="w-4 h-4 mr-2" /> 
                {showHint ? `Hint: ${currentPuzzle.hint}` : 'Get Hint'}
              </Button>
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="outline" onClick={getAIExplanation} disabled={!feedback}>
                    <Sparkles className="w-4 h-4 mr-2" /> Ask AI
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>AI Teacher's Explanation</DialogTitle>
                  </DialogHeader>
                  {isAiLoading ? (
                    <div className="flex items-center justify-center h-24">
                      <Loader2 className="w-8 h-8 animate-spin" />
                    </div>
                  ) : (
                    <p>{aiExplanation || "Ask the AI for an explanation after making a guess."}</p>
                  )}
                </DialogContent>
              </Dialog>
            </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default WordPuzzle;
