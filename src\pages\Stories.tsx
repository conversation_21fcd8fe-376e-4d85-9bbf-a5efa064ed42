import React, { useState } from 'react';
import StoryCard from '@/components/stories/StoryCard';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { useStories, Story } from '@/hooks/useStories'; // Updated import
import ConfigManager from '@/components/ConfigManager';
import EnvironmentChecker from '@/components/EnvironmentChecker';
import { Settings } from 'lucide-react';
import { Link } from 'react-router-dom';

const formSchema = z.object({
  prompt: z.string().min(5, { message: '请输入至少5个字符来描述你的故事。' }),
});

const Stories = () => {
  // Use the new centralized useStories hook
  const { stories, isLoading, error, addStory, fetchStories, setStories } = useStories();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: { prompt: '' },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    try {
      // 从 localStorage 获取 API 配置
      
      if (!aiConfigStr) {
        console.error('No ai_config found in localStorage');
        throw new Error('请先在设置中配置 AI 服务');
      }
      
      let aiConfig;
      try {
        aiConfig = JSON.parse(aiConfigStr);
        console.log('Parsed aiConfig:', aiConfig);
      } catch (parseError) {
        console.error('Error parsing ai_config:', parseError);
        throw new Error('AI 配置格式错误，请重新配置');
      }
      
      if (!aiConfig.apiKey) {
        console.error('No apiKey found in aiConfig');
        throw new Error('请先在设置中配置 API 密钥');
      }

      const response = await fetch('/api/stories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${aiConfig.apiKey}`
        },
        body: JSON.stringify({ prompt: values.prompt }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate story on the server.');
      }

      const newStory = await response.json();
      addStory(newStory); // Add the new story from the server to the state

      form.reset();
    } catch (error) {
      console.error('故事生成失败:', error);
      const errorMessage = error instanceof Error ? error.message : '生成故事时发生未知错误';
      alert(`故事生成失败: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const isAIConfigured = () => {
    try {
      console.log('Checking if AI is configured...');
      const aiConfigStr = localStorage.getItem('ai_config');
      console.log('Raw ai_config from localStorage:', aiConfigStr);
      
      if (!aiConfigStr) {
        console.log('No ai_config found in localStorage');
        return false;
      }
      
      const aiConfig = JSON.parse(aiConfigStr);
      console.log('Parsed aiConfig:', aiConfig);
      
      const isConfigured = !!(aiConfig.apiKey && aiConfig.provider);
      console.log('isAIConfigured result:', isConfigured);
      
      return isConfigured;
    } catch (error) {
      console.error('Error in isAIConfigured:', error);
      return false;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto p-6">
        {/* Header Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-4">
            📚 智能故事屋
          </h1>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            用AI生成精彩英语故事，提升阅读理解能力
          </p>
        </div>

        {/* Environment and Config Checkers */}
        <div className="mb-8 p-4 bg-white rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-3">环境与配置</h2>
          <EnvironmentChecker />
          <div className="mt-4">
            <ConfigManager />
          </div>
        </div>

        {/* Story Generation Form */}
        <Card className="p-6 mb-8 shadow-lg bg-white">
          <h2 className="text-2xl font-bold mb-4 text-gray-800">创建你的故事</h2>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="prompt"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input 
                        placeholder="例如：一只想去月球旅行的勇敢小狐狸..." 
                        {...field} 
                        className="text-lg p-4 rounded-md border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                        disabled={isSubmitting || !isAIConfigured()}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button 
                type="submit" 
                disabled={isSubmitting || !isAIConfigured()} 
                className="w-full text-lg py-6 bg-blue-600 hover:bg-blue-700 text-white font-bold rounded-md transition-transform transform hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isSubmitting ? <Loader2 className="mr-2 h-6 w-6 animate-spin" /> : '✨ 生成故事'}
              </Button>
              {!isAIConfigured() && (
                <p className="text-sm text-center text-red-500 mt-2">
                  请先在上方配置AI服务API Key和Provider以启用故事生成功能。
                </p>
              )}
            </form>
          </Form>
        </Card>

        {/* Display Stories */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-center mb-6 text-gray-800">故事画廊</h2>
          {isLoading && (
            <div className="flex justify-center items-center p-10">
              <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
              <p className="ml-4 text-lg text-gray-600">正在加载故事...</p>
            </div>
          )}
          {error && (
            <div className="text-center p-10 bg-red-100 rounded-lg">
              <p className="text-red-600 font-semibold">加载失败</p>
              <p className="text-gray-700 mt-2">{error}</p>
              <Button onClick={() => fetchStories()} className="mt-4">重试</Button>
            </div>
          )}
          {!isLoading && !error && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {stories.map((story) => (
                <StoryCard key={story.id} story={story} onDelete={(id) => {
                  // 删除后刷新本地 stories 列表
                  setStories(stories => stories.filter(s => s.id !== id));
                }} />
              ))}
            </div>
          )}
        </div>

        {/* Manage Stories Button - Always visible */}
        <div className="absolute top-0 right-0">
          <Link to="/manage-stories">
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" /> 管理故事
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Stories;
