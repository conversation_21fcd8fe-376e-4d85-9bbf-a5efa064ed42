export interface Puzzle {
  id: number;
  word: string;
  hint: string;
  image: string;
  level: string; // e.g., "Level 1: Fruits" or "AI: Animals"
  theme?: string; // To group AI-generated puzzles
}

// This can be derived from Puzzle
export interface Word {
  word: string;
  hint: string;
  imageUrl: string;
}

export interface WordPuzzleLevel {
  id: number;
  title: string;
  words: Word[];
}

export const wordPuzzleData: WordPuzzleLevel[] = [
  {
    id: 1,
    title: 'Level 1: Easy Start',
    words: [
      { word: 'apple', hint: 'A common fruit, often red or green.', imageUrl: '/assets/games/word-puzzle/apple.jpg' },
      { word: 'banana', hint: 'A long, yellow fruit.', imageUrl: '/assets/games/word-puzzle/banana.jpg' },
      { word: 'cat', hint: 'A common household pet that says "meow".', imageUrl: '/assets/games/word-puzzle/cat.jpg' },
      { word: 'dog', hint: 'A loyal pet that barks.', imageUrl: '/assets/games/word-puzzle/dog.jpg' },
      { word: 'sun', hint: 'The star at the center of our solar system.', imageUrl: '/assets/games/word-puzzle/sun.jpg' },
      { word: 'moon', hint: 'Visible in the night sky.', imageUrl: '/assets/games/word-puzzle/moon.jpg' },
    ],
  },
  {
    id: 2,
    title: 'Level 2: Animals',
    words: [
      { word: 'dog', hint: 'A common pet, known as "man\'s best friend".', imageUrl: '/assets/games/word-puzzle/dog.jpg' },
      { word: 'cat', hint: 'An independent pet that purrs.', imageUrl: '/assets/games/word-puzzle/cat.jpg' },
      { word: 'fish', hint: 'An animal that lives in water.', imageUrl: '/assets/games/word-puzzle/fish.jpg' },
      { word: 'bird', hint: 'An animal with feathers that can fly.', imageUrl: '/assets/games/word-puzzle/bird.jpg' },
      { word: 'lion', hint: 'The king of the jungle.', imageUrl: '/assets/games/word-puzzle/lion.jpg' },
      { word: 'tiger', hint: 'A large cat with stripes.', imageUrl: '/assets/games/word-puzzle/tiger.jpg' },
      { word: 'bear', hint: 'A large, heavy animal with thick fur.', imageUrl: '/assets/games/word-puzzle/bear.jpg' },
      { word: 'horse', hint: 'A large animal that people ride.', imageUrl: '/assets/games/word-puzzle/horse.jpg' },
      { word: 'snake', hint: 'A reptile with a long, thin body and no legs.', imageUrl: '/assets/games/word-puzzle/snake.jpg' },
      { word: 'monkey', hint: 'A primate that lives in trees.', imageUrl: '/assets/games/word-puzzle/monkey.jpg' },
    ],
  },
];
