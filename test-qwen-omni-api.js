// 测试Qwen-Omni API连接
import fetch from 'node-fetch';
import { HttpsProxyAgent } from 'https-proxy-agent';
import dotenv from 'dotenv';

dotenv.config();

async function testQwenOmniAPI() {
  const apiKey = process.env.DASHSCOPE_API_KEY;
  const baseUrl = 'https://dashscope.aliyuncs.com/compatible-mode/v1';
  
  console.log('Testing Qwen-Omni API...');
  console.log('API Key prefix:', apiKey ? apiKey.substring(0, 10) + '...' : 'undefined');
  console.log('Base URL:', baseUrl);
  
  // 设置代理
  let agent = null;
  if (process.env.HTTPS_PROXY) {
    agent = new HttpsProxyAgent(process.env.HTTPS_PROXY);
    console.log('Using proxy:', process.env.HTTPS_PROXY);
  } else {
    console.log('No proxy configured');
  }
  
  const requestBody = {
    model: "qwen2.5-omni-7b",
    messages: [
      {
        role: "user",
        content: "Hello, can you hear me?"
      }
    ],
    stream: true,
    stream_options: {
      include_usage: true
    },
    modalities: ["text", "audio"],
    audio: {
      voice: "Chelsie",
      format: "wav"
    }
  };

  const fetchOptions = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestBody)
  };
  
  if (agent) {
    fetchOptions.agent = agent;
  }

  try {
    console.log('Sending request...');
    const response = await fetch(`${baseUrl}/chat/completions`, fetchOptions);
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    
    console.log('✅ API connection successful!');
    
    // 读取流式响应的前几个块
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let chunkCount = 0;
    
    while (chunkCount < 5) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('Stream ended');
        break;
      }
      
      const chunk = decoder.decode(value, { stream: true });
      console.log(`Chunk ${chunkCount + 1}:`, chunk);
      chunkCount++;
    }
    
    reader.releaseLock();
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  }
}

testQwenOmniAPI();
