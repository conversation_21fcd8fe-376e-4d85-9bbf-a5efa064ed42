import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BookOpen } from 'lucide-react';
import { Link } from 'react-router-dom';
import Image from '@/components/ui/Image';

export interface Story {
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  id: string;
  hasAudio?: boolean;
  audioUrl?: string;
  titleChinese?: string;
  prompt?: string;
  userId?: string | null;
  createdAt?: string;
  updatedAt?: string;
}

interface StoryCardProps {
  story: Story;
  onDelete: (id: string) => void;
}

const StoryCard: React.FC<StoryCardProps> = ({ story, onDelete }) => {
  const handleDelete = async () => {
    if (window.confirm(`确定要删除 "${story.title}" 吗？`)) {
      const res = await fetch(`/api/stories/${story.id}`, { method: 'DELETE' });
      if (res.ok) {
        alert('删除成功');
        onDelete(story.id);
      } else {
        alert('删除失败');
      }
    }
  };

  return (
    <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-0 bg-gradient-to-br from-white to-gray-50">
      <CardHeader className="pb-3">
        <CardTitle className="line-clamp-2 text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
          {story.title}
        </CardTitle>
      </CardHeader>
      {story.imageUrl && (
        <div className="aspect-w-16 aspect-h-9 relative overflow-hidden mb-2">
          <Image
            src={story.imageUrl}
            alt={story.title}
            className="object-contain w-full h-48 transition-transform duration-300 hover:scale-105"
            loading="lazy"
            fallback="/placeholder.svg"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
        </div>
      )}
      <CardContent className="pb-4">
        <p className="text-gray-600 line-clamp-3 leading-relaxed">{story.description}</p>
      </CardContent>
      <CardFooter className="flex justify-between items-center pt-4 border-t border-gray-100">
        <Link 
          to={`/stories/${story.id}`}
          state={{ story }}
          className="flex items-center"
        >
          <Button 
            variant="ghost" 
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 transition-colors duration-200 font-medium"
          >
            <BookOpen className="w-4 h-4 mr-2" />
            阅读全文
          </Button>
        </Link>
        <Button 
          variant="destructive" 
          className="ml-2"
          onClick={handleDelete}
        >
          删除
        </Button>
      </CardFooter>
    </Card>
  );
};

export default StoryCard;
