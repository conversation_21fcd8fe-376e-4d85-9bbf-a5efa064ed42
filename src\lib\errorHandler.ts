// 防止第三方脚本干扰的工具函数
export const errorHandler = {
  // 全局错误处理器
  setupGlobalErrorHandler() {
    // 捕获未处理的错误
    window.addEventListener('error', (event) => {
      // 如果错误来自content.js（浏览器扩展），忽略它
      if (event.filename && event.filename.includes('content.js')) {
        console.warn('Ignored error from browser extension:', event.message);
        event.preventDefault();
        return false;
      }
      
      // 如果错误来自chrome-extension://, 忽略它
      if (event.filename && event.filename.startsWith('chrome-extension://')) {
        console.warn('Ignored error from chrome extension:', event.message);
        event.preventDefault();
        return false;
      }

      // 其他错误正常处理
      console.error('Application error:', event.error);
    });

    // 捕获Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
    });
  },

  // 安全地检查navigator.mediaDevices
  safeMediaDevicesCheck() {
    try {
      return !!(
        navigator &&
        navigator.mediaDevices &&
        typeof navigator.mediaDevices.getUserMedia === 'function'
      );
    } catch (error) {
      console.warn('MediaDevices check failed (likely browser extension interference):', error);
      return false;
    }
  },

  // 包装可能被第三方脚本干扰的函数
  safeCall<T>(fn: () => T, fallback: T): T {
    try {
      return fn();
    } catch (error) {
      console.warn('Safe call failed, using fallback:', error);
      return fallback;
    }
  }
};

// 在应用启动时调用
errorHandler.setupGlobalErrorHandler();
