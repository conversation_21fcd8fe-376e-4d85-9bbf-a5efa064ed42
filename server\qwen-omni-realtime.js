// 阿里云通义千问实时多模态 WebSocket 客户端
import { WebSocket } from 'ws';

class QwenOmniRealtimeClient {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.ws = null;
    this.isConnected = false;
    this.onMessage = null;
    this.onAudio = null;
    this.onError = null;
  }

  connect(model = 'qwen-omni-turbo-realtime') {
    return new Promise((resolve, reject) => {
      const wsUrl = `wss://dashscope.aliyuncs.com/api-ws/v1/realtime?model=${model}`;
      
      this.ws = new WebSocket(wsUrl, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });

      this.ws.on('open', () => {
        console.log('Connected to Qwen-Omni Realtime API');
        this.isConnected = true;
        this.sendSessionUpdate();
        resolve();
      });

      this.ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      });

      this.ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.isConnected = false;
        if (this.onError) this.onError(error);
        reject(error);
      });

      this.ws.on('close', () => {
        console.log('WebSocket connection closed');
        this.isConnected = false;
      });
    });
  }

  sendSessionUpdate() {
    const sessionConfig = {
      type: 'session.update',
      session: {
        modalities: ['text', 'audio'],
        instructions: 'You are a helpful English learning assistant.',
        voice: 'Cherry',
        turn_detection: {
          type: 'server_vad'
        }
      }
    };
    this.send(sessionConfig);
  }

  send(message) {
    if (this.ws && this.isConnected) {
      this.ws.send(JSON.stringify(message));
    }
  }

  appendAudio(audioData) {
    if (!this.isConnected) return;
    this.send({
      type: 'input_audio_buffer.append',
      audio: audioData
    });
  }

  appendImage(imageData) {
    if (!this.isConnected) return;
    this.send({
      type: 'input_image_buffer.append',
      image: imageData
    });
  }

  commitInput() {
    if (!this.isConnected) return;
    this.send({ type: 'input_audio_buffer.commit' });
  }

  generateResponse() {
    if (!this.isConnected) return;
    this.send({
      type: 'response.create',
      response: {
        modalities: ['text', 'audio']
      }
    });
  }

  handleMessage(message) {
    console.log('Received message:', message.type);
    
    switch (message.type) {
      case 'response.content_part.added':
        if (message.part && message.part.type === 'text') {
          if (this.onMessage) this.onMessage(message.part.text);
        }
        break;
      case 'response.audio.delta':
        if (message.delta && this.onAudio) {
          this.onAudio(message.delta);
        }
        break;
      case 'error':
        console.error('API Error:', message.error);
        if (this.onError) this.onError(message.error);
        break;
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.isConnected = false;
    }
  }
}

export default QwenOmniRealtimeClient;