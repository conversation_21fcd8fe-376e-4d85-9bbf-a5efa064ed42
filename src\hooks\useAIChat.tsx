import { useState, useEffect, useRef } from 'react';
import { useToast } from '@/hooks/use-toast';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  image?: string;
  timestamp: Date;
  model?: string;
}

export interface ChatHistoryItem {
  id: string;
  title: string;
  createdAt: string;
  messages: ChatMessage[];
}

const CHAT_HISTORY_KEY = 'chat_history';

export const useAIChat = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedModel, setSelectedModel] = useState('qwen-omni-turbo');
  const { toast } = useToast();
  const [isTtsLoading, setIsTtsLoading] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [activeSpeech, setActiveSpeech] = useState<{ timestamp: Date } | null>(null);

  const [history, setHistory] = useState<ChatHistoryItem[]>([]);
  const [currentChatId, setCurrentChatId] = useState<string | null>(null);

  // Effect 1: Load history from localStorage on initial mount
  useEffect(() => {
    const raw = localStorage.getItem(CHAT_HISTORY_KEY);
    if (raw) {
      try {
        const loadedHistory = JSON.parse(raw);
        setHistory(loadedHistory);
        if (loadedHistory.length > 0) {
          setCurrentChatId(loadedHistory[0].id);
        } else {
          setCurrentChatId('chat_' + Date.now());
        }
      } catch (e) {
        console.error("Failed to parse chat history:", e);
        setCurrentChatId('chat_' + Date.now());
      }
    } else {
      setCurrentChatId('chat_' + Date.now());
    }
  }, []);

  // Effect 2: Load messages when the currentChatId changes
  useEffect(() => {
    if (!currentChatId) return;

    const item = history.find(h => h.id === currentChatId);
    if (item) {
      const fixedMessages = item.messages.map(msg => ({
        ...msg,
        timestamp: new Date(msg.timestamp),
      }));
      setMessages(fixedMessages);
    } else {
      setMessages([]);
    }
  }, [currentChatId]);

  // Effect 3: Auto-save messages to history and localStorage
  useEffect(() => {
    if (!currentChatId || messages.length === 0) {
      return;
    }

    const newHistory = [...history];
    const idx = newHistory.findIndex(h => h.id === currentChatId);
    
    const title = messages[0]?.content?.slice(0, 20) || 'New Chat';
    const item: ChatHistoryItem = {
      id: currentChatId,
      title,
      createdAt: messages[0]?.timestamp?.toISOString?.() || new Date().toISOString(),
      messages: messages,
    };

    if (idx !== -1) {
      newHistory[idx] = item;
    } else {
      newHistory.unshift(item);
    }
    
    setHistory(newHistory);
    localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(newHistory));
  }, [messages]);

  const loadHistory = (id: string) => {
    setCurrentChatId(id);
  };

  const deleteChat = (chatIdToDelete: string) => {
    const updatedHistory = history.filter(chat => chat.id !== chatIdToDelete);
    setHistory(updatedHistory);
    localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(updatedHistory));

    if (currentChatId === chatIdToDelete) {
      if (updatedHistory.length > 0) {
        loadHistory(updatedHistory[0].id);
      } else {
        startNewChat();
      }
    }
  };

  const startNewChat = () => {
    const newId = 'chat_' + Date.now();
    setCurrentChatId(newId);
  };

  const clearMessages = () => {
      if (!currentChatId) return;
      const newHistory = history.filter(h => h.id !== currentChatId);
      setHistory(newHistory);
      localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(newHistory));

      if (newHistory.length > 0) {
          setCurrentChatId(newHistory[0].id);
      } else {
          startNewChat();
      }
  };

  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  const sendMessage = async (message: { text: string; image?: string; audio?: string }) => {
    if ((!message.text.trim() && !message.image && !message.audio) || !currentChatId) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: message.text || (message.audio ? '[Voice Message]' : '[Image]'),
      image: message.image,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setLoading(true);

    try {
      const requestBody: any = {
        message: message.text,
        model: selectedModel,
        conversationHistory: messages.map(msg => ({ role: msg.role, content: msg.content })),
      };

      // 如果是多模态模型且有图片或音频，添加多模态数据
      if (selectedModel === 'qwen-omni-turbo') {
        if (message.image) {
          requestBody.image = message.image;
        }
        if (message.audio) {
          requestBody.audio = message.audio;
        }
      }

      const response = await fetch('http://localhost:3001/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get response from server');
      }

      const data = await response.json();

      const aiMessage: ChatMessage = {
        role: 'assistant',
        content: data.response,
        timestamp: new Date(),
        model: data.model,
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        variant: "destructive",
        title: "发送失败",
        description: "无法发送消息，请稍后重试"
      });
    } finally {
      setLoading(false);
    }
  };

  const playTextToSpeech = async (text: string) => {
    if (isTtsLoading) return;
    setIsTtsLoading(true);
    try {
      // 使用浏览器原生 TTS 作为临时替代
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'en-US';
      utterance.rate = 0.9;
      speechSynthesis.speak(utterance);
      utterance.onend = () => setIsTtsLoading(false);
      utterance.onerror = () => setIsTtsLoading(false);
    } catch (err: any) {
      toast({ variant: "destructive", title: "语音播放失败", description: err.message });
      setIsTtsLoading(false);
    }
  };

  const toggleMessagePlayback = async (message: ChatMessage) => {
    const { timestamp, content } = message;
    if (activeSpeech?.timestamp.getTime() === timestamp.getTime()) {
      speechSynthesis.cancel();
      setActiveSpeech(null);
      return;
    }
    
    speechSynthesis.cancel();
    setActiveSpeech({ timestamp });
    try {
      // 使用浏览器原生 TTS
      const utterance = new SpeechSynthesisUtterance(content);
      utterance.lang = 'en-US';
      utterance.rate = 0.9;
      utterance.onend = () => setActiveSpeech(null);
      utterance.onerror = () => setActiveSpeech(null);
      speechSynthesis.speak(utterance);
    } catch (err: any) {
      toast({ variant: "destructive", title: "语音播放失败", description: err.message });
      setActiveSpeech(null);
    }
  };

  return {
    messages,
    loading,
    isTtsLoading,
    sendMessage,
    clearMessages,
    selectedModel,
    setSelectedModel,
    playTextToSpeech,
    toggleMessagePlayback,
    activeSpeech: activeSpeech ? { timestamp: activeSpeech.timestamp, isPaused: false } : null,
    chatHistory: history,
    loadChat: loadHistory,
    currentChatId,
    startNewChat,
    deleteChat
  };
};