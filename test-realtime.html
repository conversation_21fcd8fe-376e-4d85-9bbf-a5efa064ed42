<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阿里云通义千问实时聊天测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #ff4444;
        }
        .status-dot.connected {
            background-color: #44ff44;
        }
        .chat-area {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .message {
            margin-bottom: 15px;
            display: flex;
        }
        .message.user {
            justify-content: flex-end;
        }
        .message-content {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 15px;
            word-wrap: break-word;
        }
        .message.user .message-content {
            background: #007bff;
            color: white;
        }
        .message.assistant .message-content {
            background: #f1f1f1;
            color: #333;
        }
        .input-area {
            padding: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        input[type="text"] {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        input[type="text"]:focus {
            border-color: #007bff;
        }
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .recording {
            background: #dc3545 !important;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>阿里云通义千问实时聊天测试</h1>
            <div class="status">
                <span>连接状态:</span>
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">未连接</span>
                <button id="connectBtn" class="btn-primary">连接</button>
            </div>
        </div>
        
        <div class="chat-area" id="chatArea">
            <div style="text-align: center; color: #666; margin-top: 150px;">
                <h3>欢迎使用阿里云通义千问实时聊天</h3>
                <p>支持文本、语音和图片输入</p>
                <p>请先点击"连接"按钮建立WebSocket连接</p>
            </div>
        </div>
        
        <div class="input-area">
            <input type="file" id="imageInput" accept="image/*" style="display: none;">
            <button id="imageBtn" class="btn-secondary" disabled>📷 图片</button>
            <input type="text" id="textInput" placeholder="输入消息..." disabled>
            <button id="recordBtn" class="btn-secondary" disabled>🎤 录音</button>
            <button id="sendBtn" class="btn-primary" disabled>发送</button>
        </div>
    </div>

    <script>
        class RealtimeChat {
            constructor() {
                this.ws = null;
                this.isConnected = false;
                this.isRecording = false;
                this.mediaRecorder = null;
                this.audioChunks = [];
                
                this.initializeElements();
                this.bindEvents();
            }
            
            initializeElements() {
                this.statusDot = document.getElementById('statusDot');
                this.statusText = document.getElementById('statusText');
                this.connectBtn = document.getElementById('connectBtn');
                this.chatArea = document.getElementById('chatArea');
                this.textInput = document.getElementById('textInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.recordBtn = document.getElementById('recordBtn');
                this.imageBtn = document.getElementById('imageBtn');
                this.imageInput = document.getElementById('imageInput');
            }
            
            bindEvents() {
                this.connectBtn.addEventListener('click', () => {
                    if (this.isConnected) {
                        this.disconnect();
                    } else {
                        this.connect();
                    }
                });
                
                this.sendBtn.addEventListener('click', () => this.sendMessage());
                this.textInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendMessage();
                });
                
                this.recordBtn.addEventListener('click', () => {
                    if (this.isRecording) {
                        this.stopRecording();
                    } else {
                        this.startRecording();
                    }
                });
                
                this.imageBtn.addEventListener('click', () => {
                    this.imageInput.click();
                });
                
                this.imageInput.addEventListener('change', (e) => {
                    this.handleImageSelect(e);
                });
            }
            
            connect() {
                try {
                    console.log('尝试连接WebSocket...');
                    this.ws = new WebSocket('ws://localhost:3002');
                    
                    this.ws.onopen = () => {
                        console.log('WebSocket连接成功');
                        this.isConnected = true;
                        this.updateConnectionStatus();
                        this.clearChat();
                        this.addMessage('system', '✅ 连接成功！现在可以开始对话了');
                    };
                    
                    this.ws.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        console.log('收到消息:', data);
                        this.handleWebSocketMessage(data);
                    };
                    
                    this.ws.onclose = () => {
                        console.log('WebSocket连接断开');
                        this.isConnected = false;
                        this.updateConnectionStatus();
                        this.addMessage('system', '❌ 连接已断开');
                    };
                    
                    this.ws.onerror = (error) => {
                        console.error('WebSocket错误:', error);
                        this.addMessage('system', '❌ 连接失败，请检查后端服务器是否启动');
                    };
                    
                } catch (error) {
                    console.error('连接失败:', error);
                    this.addMessage('system', '❌ 连接失败: ' + error.message);
                }
            }
            
            disconnect() {
                if (this.ws) {
                    this.ws.close();
                    this.ws = null;
                }
                this.isConnected = false;
                this.updateConnectionStatus();
            }
            
            updateConnectionStatus() {
                if (this.isConnected) {
                    this.statusDot.classList.add('connected');
                    this.statusText.textContent = '已连接';
                    this.connectBtn.textContent = '断开';
                    this.connectBtn.className = 'btn-danger';
                    
                    // 启用输入控件
                    this.textInput.disabled = false;
                    this.sendBtn.disabled = false;
                    this.recordBtn.disabled = false;
                    this.imageBtn.disabled = false;
                } else {
                    this.statusDot.classList.remove('connected');
                    this.statusText.textContent = '未连接';
                    this.connectBtn.textContent = '连接';
                    this.connectBtn.className = 'btn-primary';
                    
                    // 禁用输入控件
                    this.textInput.disabled = true;
                    this.sendBtn.disabled = true;
                    this.recordBtn.disabled = true;
                    this.imageBtn.disabled = true;
                }
            }
            
            handleWebSocketMessage(data) {
                switch (data.type) {
                    case 'session_ready':
                        this.addMessage('system', '🎉 会话已准备就绪');
                        break;
                    case 'text_delta':
                        this.handleTextDelta(data.content);
                        break;
                    case 'audio_delta':
                        this.handleAudioDelta(data.content);
                        break;
                    case 'response_done':
                        this.handleResponseDone();
                        break;
                    case 'error':
                        this.addMessage('system', '❌ 错误: ' + data.error);
                        break;
                }
            }
            
            handleTextDelta(content) {
                // 找到最后一条assistant消息或创建新的
                const messages = this.chatArea.querySelectorAll('.message.assistant:last-child .message-content');
                if (messages.length > 0) {
                    const lastMessage = messages[messages.length - 1];
                    if (lastMessage.classList.contains('partial')) {
                        lastMessage.textContent += content;
                        return;
                    }
                }
                
                // 创建新的部分消息
                this.addMessage('assistant', content, true);
            }
            
            handleAudioDelta(audioData) {
                // 这里可以处理音频播放
                console.log('收到音频数据:', audioData);
            }
            
            handleResponseDone() {
                // 移除partial标记
                const partialMessages = this.chatArea.querySelectorAll('.message-content.partial');
                partialMessages.forEach(msg => msg.classList.remove('partial'));
            }
            
            sendMessage() {
                const text = this.textInput.value.trim();
                if (!text) return;
                
                this.addMessage('user', text);
                
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify({
                        type: 'text_message',
                        content: text
                    }));
                }
                
                this.textInput.value = '';
            }
            
            async startRecording() {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ 
                        audio: { 
                            sampleRate: 16000,
                            channelCount: 1 
                        } 
                    });
                    
                    this.mediaRecorder = new MediaRecorder(stream);
                    this.audioChunks = [];
                    
                    this.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            this.audioChunks.push(event.data);
                            
                            // 发送音频块到服务器
                            event.data.arrayBuffer().then(buffer => {
                                const base64Audio = this.arrayBufferToBase64(buffer);
                                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                                    this.ws.send(JSON.stringify({
                                        type: 'audio_chunk',
                                        audio: base64Audio
                                    }));
                                }
                            });
                        }
                    };
                    
                    this.mediaRecorder.start(100);
                    this.isRecording = true;
                    this.recordBtn.textContent = '🛑 停止';
                    this.recordBtn.classList.add('recording');
                    
                    this.addMessage('system', '🎤 开始录音...');
                    
                } catch (error) {
                    console.error('录音失败:', error);
                    this.addMessage('system', '❌ 录音失败: ' + error.message);
                }
            }
            
            stopRecording() {
                if (this.mediaRecorder && this.isRecording) {
                    this.mediaRecorder.stop();
                    this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
                    
                    // 发送录音结束信号
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({ type: 'audio_end' }));
                    }
                    
                    this.isRecording = false;
                    this.recordBtn.textContent = '🎤 录音';
                    this.recordBtn.classList.remove('recording');
                    
                    this.addMessage('system', '🎤 录音结束');
                }
            }
            
            handleImageSelect(event) {
                const file = event.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    const imageData = e.target.result;
                    this.addMessage('user', '📷 发送了图片');
                    
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({
                            type: 'text_message',
                            content: '请分析这张图片',
                            image: imageData
                        }));
                    }
                };
                reader.readAsDataURL(file);
            }
            
            addMessage(type, content, isPartial = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = `message-content ${isPartial ? 'partial' : ''}`;
                contentDiv.textContent = content;
                
                messageDiv.appendChild(contentDiv);
                this.chatArea.appendChild(messageDiv);
                
                // 滚动到底部
                this.chatArea.scrollTop = this.chatArea.scrollHeight;
            }
            
            clearChat() {
                this.chatArea.innerHTML = '';
            }
            
            arrayBufferToBase64(buffer) {
                const bytes = new Uint8Array(buffer);
                let binary = '';
                for (let i = 0; i < bytes.byteLength; i++) {
                    binary += String.fromCharCode(bytes[i]);
                }
                return btoa(binary);
            }
        }
        
        // 初始化聊天
        document.addEventListener('DOMContentLoaded', () => {
            new RealtimeChat();
        });
    </script>
</body>
</html>