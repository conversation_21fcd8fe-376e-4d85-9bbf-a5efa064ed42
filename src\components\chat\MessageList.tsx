
import React, { useRef, useEffect, useLayoutEffect } from 'react';
import { Bo<PERSON>, User, PlayCircle, PauseCircle } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import ClickableText from '@/components/ClickableText';
import type { ChatMessage } from '@/hooks/useAIChat';

interface MessageListProps {
  messages: ChatMessage[];
  loading: boolean;
  activeSpeech: { timestamp: Date; isPaused: boolean } | null;
  onWordClick: (word: string, context: string) => void;
  toggleMessagePlayback: (message: ChatMessage) => void;
}

const MessageList = ({
  messages,
  loading,
  activeSpeech,
  onWordClick,
  toggleMessagePlayback,
}: MessageListProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const viewportRef = useRef<HTMLElement | null>(null);
  const userScrolledUpRef = useRef(false);
  const autoscrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Find the viewport on mount by traversing up from a known element
  useEffect(() => {
    if (messagesEndRef.current) {
      const viewport = messagesEndRef.current.parentElement?.parentElement;
      if (viewport) {
        viewportRef.current = viewport;
        const handleScroll = () => {
          // If a scroll is triggered by our code, don't update the user scrolled state
          if (autoscrollTimeoutRef.current) return;
          const { scrollTop, scrollHeight, clientHeight } = viewport;
          // Check if user is scrolled up from the bottom with a buffer
          const isAtBottom = scrollHeight - scrollTop - clientHeight < 150;
          userScrolledUpRef.current = !isAtBottom;
        };
        viewport.addEventListener('scroll', handleScroll, { passive: true });
        return () => viewport.removeEventListener('scroll', handleScroll);
      }
    }
  }, []);

  useLayoutEffect(() => {
    const lastMessage = messages[messages.length - 1];
    const isUserMessage = lastMessage?.role === 'user';

    // If the user sends a message, we should always scroll to the bottom
    // and reset the scrolled-up lock.
    if (isUserMessage) {
      userScrolledUpRef.current = false;
    }

    // If the user has scrolled up, don't auto-scroll for assistant messages.
    if (userScrolledUpRef.current && !isUserMessage) {
      return;
    }

    // Use a timeout to debounce scrolling and prevent jerky movements.
    if (autoscrollTimeoutRef.current) {
      clearTimeout(autoscrollTimeoutRef.current);
    }

    autoscrollTimeoutRef.current = setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      autoscrollTimeoutRef.current = null;
    }, 50);

    return () => {
      if (autoscrollTimeoutRef.current) {
        clearTimeout(autoscrollTimeoutRef.current);
      }
    };
  }, [messages]);

  return (
    <ScrollArea className="flex-1 p-4">
      <div className="space-y-4">
        {messages.map((message, index) => {
          const isPlaying = activeSpeech?.timestamp.getTime() === message.timestamp.getTime();
          const isPaused = isPlaying && activeSpeech?.isPaused;

          return (
            <div
              key={index}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`flex items-start space-x-2 max-w-[80%] ${
                  message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center shrink-0 ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-emerald-600 text-white'
                  }`}
                >
                  {message.role === 'user' ? (
                    <User className="w-4 h-4" />
                  ) : (
                    <Bot className="w-4 h-4" />
                  )}
                </div>
                <div
                  className={`p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <div className="flex flex-col gap-2">
                    {message.image && (
                      <img 
                        src={message.image} 
                        alt="User upload" 
                        className="rounded-lg max-w-xs max-h-64 object-contain cursor-pointer" 
                        onClick={() => {
                          const newTab = window.open();
                          newTab?.document.write(`<body style="margin:0; background: #111;"><img src="${message.image}" style="width:100%; height:100%; object-fit:contain;"></body>`);
                        }}
                      />
                    )}
                    {message.content && (message.role === 'assistant' ? (
                      <ClickableText text={message.content} onWordClick={onWordClick} />
                    ) : (
                      <p className="whitespace-pre-wrap">{message.content}</p>
                    ))}
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <p className="text-xs opacity-70">
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                    <div className="flex items-center space-x-1">
                      {message.model && message.role === 'assistant' && (
                        <p className="text-xs opacity-70">{message.model}</p>
                      )}
                      {message.role === 'assistant' && (
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => toggleMessagePlayback(message)}
                          className="w-6 h-6 text-gray-500 hover:text-gray-900"
                        >
                          {isPlaying && !isPaused ? (
                            <PauseCircle className="w-4 h-4" />
                          ) : (
                            <PlayCircle className="w-4 h-4" />
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {loading && (
          <div className="flex justify-start">
            <div className="flex items-start space-x-2">
              <div className="w-8 h-8 rounded-full bg-emerald-600 text-white flex items-center justify-center">
                <Bot className="w-4 h-4" />
              </div>
              <div className="bg-gray-100 p-3 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      <div ref={messagesEndRef} />
    </ScrollArea>
  );
};

export default MessageList;
