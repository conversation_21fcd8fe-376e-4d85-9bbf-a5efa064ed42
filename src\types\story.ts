// 故事类型定义
export interface Story {
  id: string;
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  audioUrl?: string; // Add this line
  hasAudio?: boolean;
  createdAt: string;
  updatedAt: string;
}

// 本地存储的故事类型
export interface LocalStory extends Story {
  lastAccessed: string;
}

// 生成的故事结果类型
export interface StoryGenerationResult {
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  imagePrompt?: string;
}
