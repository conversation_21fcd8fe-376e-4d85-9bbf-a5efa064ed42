import { useState, useEffect, useCallback } from 'react';
import sampleStories from '@/data/sampleStories.json';

// Define the Story type based on your data structure
export interface Story {
  id: string;
  title: string;
  titleChinese?: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
  audioUrl?: string;
  prompt?: string;
  userId?: string | null;
  createdAt: string;
  updatedAt: string;
  hasAudio?: boolean; // This can be derived on the client or sent from server
}

export const useStories = () => {
  const [stories, setStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStories = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Use the proxied API endpoint
      const response = await fetch('/api/stories');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data: Story[] = await response.json();
      
      // The server now provides all stories, including samples.
      // The image and audio URLs should already be correct.
      setStories(data);

    } catch (e: any) {
      console.error("Failed to fetch stories from local server:", e);
      setError('无法从本地服务器加载故事。正在使用静态示例故事作为备用。');
      // Fallback to static sample stories if the server fails
      // Add audioUrl and hasAudio to sample stories
      const storiesWithAudio = sampleStories.map(story => ({
        ...story,
        audioUrl: `/uploads/audio/${story.id}.mp3`,
        hasAudio: true
      }));
      setStories(storiesWithAudio);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStories();
  }, [fetchStories]);

  // Function to add a new story to the list, e.g., after generation
  const addStory = (newStory: Story) => {
    setStories(prevStories => [newStory, ...prevStories]);
  };

  // Function to update a story, e.g., after adding audio
  const updateStory = (updatedStory: Story) => {
    setStories(prevStories =>
      prevStories.map(story => (story.id === updatedStory.id ? updatedStory : story))
    );
  };

  return { stories, isLoading, error, fetchStories, addStory, updateStory };
};