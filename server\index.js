// server/index.js - 最终完整修复版 v3

import express from 'express';
import cors from 'cors';
import fs from 'fs/promises';
import { existsSync, createWriteStream, unlinkSync, createReadStream } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import dotenv from 'dotenv';
import axios from 'axios';
import fetch from 'node-fetch';
import { HttpsProxyAgent } from 'https-proxy-agent';
import multer from 'multer';
import FormData from 'form-data';
import { GoogleGenAI } from "@google/genai";
import { WebSocketServer } from 'ws';
import { QwenRealtimeClient } from './qwen-http-realtime.js';

console.log("--- SERVER.JS V4 IS RUNNING ---"); // <--- 添加这一行作为标记
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const port = 3001;

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use('/uploads', express.static(path.join(__dirname, '..', 'public', 'uploads')));
app.use(express.static(path.join(__dirname, '../dist')));

const sampleStoriesPath = path.join(__dirname, '..', 'src', 'data', 'sampleStories.json');
const generatedStoriesPath = path.join(__dirname, 'generated-stories.json');
const generatedPuzzlesPath = path.join(__dirname, 'generated-puzzles.json');

// Corrected SSE Parser
function parseDashScopeSseResponse(sseText) {
  let fullContent = '';
  const lines = sseText.split('\n').filter(line => line.trim().startsWith('data:'));
  for (const line of lines) {
    try {
      const jsonStr = line.substring(5).trim();
      if (jsonStr) {
        const data = JSON.parse(jsonStr);
        if (data.output && data.output.choices && data.output.choices[0].message.content) {
          fullContent = data.output.choices[0].message.content;
        }
      }
    } catch (e) { /* ignore */ }
  }
  return fullContent.trim();
}

const getSampleStories = async () => { try { const c = await fs.readFile(sampleStoriesPath, 'utf-8'); return JSON.parse(c); } catch (e) { console.error('Error reading sample stories:', e); return []; } };
const getGeneratedStories = async () => { try { const c = await fs.readFile(generatedStoriesPath, 'utf-8'); return JSON.parse(c); } catch (e) { if (e.code === 'ENOENT') return []; console.error('Error reading generated stories:', e); return []; } };
const getGeneratedPuzzles = async () => { try { await fs.access(generatedPuzzlesPath); const c = await fs.readFile(generatedPuzzlesPath, 'utf-8'); if (c.trim() === '') return []; const d = JSON.parse(c); return Array.isArray(d) ? d : Object.values(d); } catch (e) { if (e.code === 'ENOENT') { await fs.writeFile(generatedPuzzlesPath, '[]', 'utf-8'); } return []; } };

const pollTaskStatus = async (taskId, apiKey) => {
  const url = `https://dashscope.aliyuncs.com/api/v1/tasks/${taskId}`;
  while (true) {
    console.log(`[POLLING] Task ${taskId}...`);
    await new Promise(resolve => setTimeout(resolve, 3000));
    const response = await fetch(url, { headers: { 'Authorization': `Bearer ${apiKey}` } });
    const result = await response.json();
    if (response.status !== 200 || !result.output) { throw new Error(`Polling failed`); }
    const taskStatus = result.output.task_status;
    if (taskStatus === 'SUCCEEDED') { console.log('[POLLING] Task Succeeded!'); return result.output.results[0].url; }
    if (taskStatus === 'FAILED') { console.error('[POLLING] Task Failed:', result.output.message); throw new Error(`Image generation failed: ${result.output.message}`); }
  }
};

async function generateImage(prompt, apiKey) {
  try {
    const modelId = 'wanx2.0-t2i-turbo';
    console.log(`[IMAGE] Using model: ${modelId}`);
    const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis', { method: 'POST', headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${apiKey}`, 'X-DashScope-Async': 'enable' }, body: JSON.stringify({ model: modelId, input: { prompt: `Children's storybook illustration, cute cartoon style, about: ${prompt}` }, parameters: { n: 1, size: '1024*1024' } }) });
    const data = await response.json();
    if (response.status !== 200 || !data.output || !data.output.task_id) { console.error('[IMAGE] Task submission failed:', data); throw new Error('Image task submission failed'); }
    const taskId = data.output.task_id;
    console.log(`[IMAGE] Task ID: ${taskId}`);
    const imageUrl = await pollTaskStatus(taskId, apiKey);
    const imageResponse = await fetch(imageUrl);
    const imageDir = path.join(__dirname, '..', 'public', 'uploads', 'images');
    await fs.mkdir(imageDir, { recursive: true });
    const imageName = `image-${Date.now()}.png`;
    const imagePath = path.join(imageDir, imageName);
    const fileStream = createWriteStream(imagePath);
    await new Promise((resolve, reject) => { imageResponse.body.pipe(fileStream); imageResponse.body.on("error", reject); fileStream.on("finish", resolve); });
    const localImageUrl = `/uploads/images/${imageName}`;
    console.log(`[IMAGE] Saved locally: ${localImageUrl}`);
    return localImageUrl;
  } catch (error) { console.error('[IMAGE] Error:', error.message); return '/placeholder.svg'; }
}

// server/index.js

// **The Definitive, Ground-Truth generateAudio FUNCTION - V6 (cURL Corrected)**
async function generateAudio(text, apiKey) {
    try {
        if (!text || text.trim() === '') throw new Error("Input text is empty.");
        const modelId = 'qwen-tts';
        console.log(`[AUDIO] Using model: ${modelId}`);

        // THE CORRECT PAYLOAD STRUCTURE, MIMICKING THE OFFICIAL cURL EXAMPLE.
        const dashscopeParams = {
            model: modelId,
            input: {
                text: text,
                voice: 'Serena' // 'voice' is inside the 'input' object.
                // Note: We are omitting 'format' and 'sample_rate' to be
                // exactly like the cURL example. The API will use defaults.
                // The default format is 'wav'.
            }
            // NO 'parameters' object!
        };
        console.log('[AUDIO] dashscope params being sent (cURL version):', dashscopeParams);

        // THE CORRECT URL from the cURL example.
        const response = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${apiKey}` },
            body: JSON.stringify(dashscopeParams)
        });

        if (!response.ok) {
            const errorBody = await response.json();
            console.error('[AUDIO] API call failed. Status:', response.status, 'Body:', errorBody);
            throw new Error(`TTS API failed: ${errorBody.message || JSON.stringify(errorBody)}`);
        }
        
        const responseData = await response.json();
        // The response structure for this endpoint should be correct.
        const audioUrlFromApi = responseData.output?.audio?.url;

        if (!audioUrlFromApi) {
            console.error('[AUDIO] Could not find audio URL in API response:', responseData);
            throw new Error('Could not find audio URL in API response.');
        }
        
        console.log(`[AUDIO] Received audio URL successfully: ${audioUrlFromApi}`);
        const audioResponse = await fetch(audioUrlFromApi);
        if (!audioResponse.ok) { throw new Error(`Failed to download audio file from ${audioUrlFromApi}`); }
        
        const audioDir = path.join(__dirname, '..', 'public', 'uploads', 'audio');
        await fs.mkdir(audioDir, { recursive: true });
        // The default format is 'wav', so we save as .wav.
        const audioFileName = `audio-${Date.now()}.wav`; 
        const audioFilePath = path.join(audioDir, audioFileName);
        const fileStream = createWriteStream(audioFilePath);
        
        await new Promise((resolve, reject) => {
            audioResponse.body.pipe(fileStream);
            audioResponse.body.on("error", reject);
            fileStream.on("finish", resolve);
        });

        const localAudioUrl = `/uploads/audio/${audioFileName}`;
        console.log(`[AUDIO] Saved locally: ${localAudioUrl}`);
        return localAudioUrl;
    } catch (error) {
        console.error('[AUDIO] FINAL ERROR in generateAudio:', error.message);
        return null;
    }
}


// All other routes are here and correct
app.get('/api/stories', async (req, res) => { try { const sampleStories = await getSampleStories(); const generatedStories = await getGeneratedStories(); res.json([...generatedStories, ...sampleStories]); } catch (error) { res.status(500).json({ message: 'Error fetching stories' }); } });
app.get('/api/stories/:id', async (req, res) => { try { const storyId = req.params.id; const allStories = [...await getGeneratedStories(), ...await getSampleStories()]; const story = allStories.find(s => s.id === storyId); if (story) { res.json(story); } else { res.status(404).json({ message: 'Story not found' }); } } catch (error) { res.status(500).json({ message: 'Error fetching story' }); } });

app.post('/api/stories', async (req, res) => {
  const { prompt } = req.body;
  if (!prompt) return res.status(400).json({ message: 'Prompt is required' });
  try {
    console.log(`\n--- NEW STORY REQUEST: "${prompt}" ---`);
    const apiKey = req.headers.authorization?.replace('Bearer ', '');
    if (!apiKey) return res.status(401).json({ message: 'Missing API key' });

    console.log('[STEP 1] Generating English story...');

    // 配置代理
    const proxyUrl = process.env.HTTPS_PROXY;
    let agent = undefined;
    if (proxyUrl) {
      const { HttpsProxyAgent } = await import('https-proxy-agent');
      agent = new HttpsProxyAgent(proxyUrl);
    }

    const storyResponse = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
      method: 'POST',
      agent: agent,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'qwen-turbo',
        messages: [
          { role: 'system', content: 'You are a professional story writer.' },
          { role: 'user', content: `Write a short story in simple English about "${prompt}".` }
        ],
        stream: false
      })
    });
    if (!storyResponse.ok) throw new Error('Story generation failed');
    const storyData = await storyResponse.json();
    let storyText = storyData.choices?.[0]?.message?.content || '';
    if (!storyText) throw new Error('Generated story is empty.');
    console.log('[STEP 1] English story GENERATED.');

    console.log('[STEP 2] Generating Chinese translation...');
    const translationResponse = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
      method: 'POST',
      agent: agent,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'qwen-turbo',
        messages: [
          { role: 'system', content: 'You are a professional translator.' },
          { role: 'user', content: `Translate the following English story into Chinese:\n\n${storyText}` }
        ],
        stream: false
      })
    });
    let chineseContent = `关于“${prompt}”的故事`;
    if (translationResponse.ok) {
      const translationData = await translationResponse.json();
      chineseContent = translationData.choices?.[0]?.message?.content || chineseContent;
    }
    console.log('[STEP 2] Chinese translation GENERATED.');

    console.log('[STEP 3] Starting Image and Audio generation IN PARALLEL...');
    const [imageUrl, audioUrl] = await Promise.all([
        generateImage(storyText.substring(0, 300), apiKey),
        generateAudio(storyText, apiKey)
    ]);
    console.log(`[STEP 4] PARALLEL generation FINISHED.`);

    const newStory = { id: `generated-${Date.now()}`, title: `A Story About "${prompt.substring(0, 20)}..."`, titleChinese: `一个关于"${prompt.substring(0, 10)}..."的故事`, description: `A story generated from the prompt: "${prompt}"`, story: storyText, storyChinese: chineseContent, imageUrl, audioUrl, prompt, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() };
    const generatedStories = await getGeneratedStories();
    generatedStories.unshift(newStory);
    await fs.writeFile(generatedStoriesPath, JSON.stringify(generatedStories, null, 2));
    console.log('[STEP 5] Sending final response to client.');
    res.status(201).json(newStory);
  } catch (error) {
    console.error('!!! TOP LEVEL ERROR in /api/stories:', error.message);
    res.status(500).json({ message: 'Failed to generate story', error: error.message });
  }
});

const upload = multer({ dest: 'uploads/' });

// 新增：专门用于实时聊天的语音识别API
app.post('/api/speech-recognition', async (req, res) => {
  try {
    const { audioData } = req.body; // base64编码的音频数据

    if (!audioData) {
      return res.status(400).json({ message: 'No audio data provided.' });
    }

    if (!process.env.DASHSCOPE_API_KEY) {
      throw new Error('DASHSCOPE_API_KEY not configured in environment variables.');
    }

    console.log('[Speech Recognition API] Processing audio data...');

    // 将base64音频数据转换为Buffer
    const audioBuffer = Buffer.from(audioData, 'base64');

    // 创建临时文件
    const tempFilePath = path.join('uploads', `temp_audio_${Date.now()}.wav`);
    await fs.writeFile(tempFilePath, audioBuffer);

    // 调用阿里云语音识别API
    const asrForm = new FormData();
    asrForm.append('model', 'paraformer-v1');
    asrForm.append('file', createReadStream(tempFilePath));

    const asrResponse = await fetch('https://dashscope.aliyuncs.com/api/v1/services/audio/asr/recognition', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
        ...asrForm.getHeaders()
      },
      body: asrForm
    });

    const asrData = await asrResponse.json();

    // 清理临时文件
    try {
      await fs.unlink(tempFilePath);
    } catch (e) {
      console.warn('Failed to delete temp file:', e.message);
    }

    // 解析识别结果
    let recognizedText = '';
    if (asrData.output && asrData.output.transcription) {
      recognizedText = asrData.output.transcription.text;
    } else if (asrData.output && asrData.output.sentence_list) {
      recognizedText = asrData.output.sentence_list.map(s => s.text).join(' ');
    } else {
      throw new Error('语音识别失败: ' + (asrData.message || '未知错误'));
    }

    console.log('[Speech Recognition API] Recognized text:', recognizedText);

    res.json({
      success: true,
      text: recognizedText,
      message: 'Speech recognition completed successfully'
    });

  } catch (error) {
    console.error('[Speech Recognition API] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Speech recognition failed',
      error: error.message
    });
  }
});

app.post('/api/score-speaking', upload.single('audio'), async (req, res) => {
  const useOmni = req.body.useOmni === 'true' || req.query.useOmni === 'true';
  try {
    const audioFile = req.file;
    const standardTranscript = req.body.transcript;
    if (!audioFile) {
      return res.status(400).json({ message: 'No audio file uploaded.' });
    }
    if (!process.env.DASHSCOPE_API_KEY) {
      throw new Error('DASHSCOPE_API_KEY not configured in environment variables.');
    }

    if (useOmni) {
      // --- Qwen-Omni 方案 ---
      const audioBuffer = await fs.readFile(audioFile.path);
      const ext = path.extname(audioFile.originalname || audioFile.filename || '').replace('.', '').toLowerCase() || 'mp3';
      const supportedFormats = ['mp3', 'wav', 'ogg', 'webm'];
      const format = supportedFormats.includes(ext) ? ext : 'mp3';
      const base64Audio = audioBuffer.toString('base64');
      const dataUrl = `data:;base64,${base64Audio}`;
      const messages = [
        {
          role: 'user',
          content: [
            {
              type: 'input_audio',
              input_audio: { data: dataUrl, format },
            },
            {
              type: 'text',
              text: `请对比下列标准文本和音频内容，给出口语准确度、完整度、流利度评分（0-100），并用中文给出反馈。标准文本："${standardTranscript}"。请严格以JSON格式返回，包含 accuracy, completeness, fluency, feedback 四个字段。`
            }
          ]
        }
      ];
      const response = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'qwen-omni-turbo',
          messages: messages,
          // 加上这一行：
          stream: true
        })
      });
      if (!response.ok) throw new Error('Qwen-Omni API 调用失败');
      let fullText = '';
      const decoder = new TextDecoder();
      for await (const chunk of response.body) {
        fullText += decoder.decode(chunk, { stream: true });
      }
      console.log('Qwen-Omni fullText:', fullText); // <-- 新增
      const lines = fullText.split('\n').filter(line => line.trim().startsWith('data:'));
      console.log('Qwen-Omni lines:', lines); // <-- 新增
      let omniContent = '';
      for (const line of lines) {
        try {
          const jsonStr = line.substring(5).trim();
          if (jsonStr && jsonStr !== '[DONE]') {
            const data = JSON.parse(jsonStr);
            const content = data.choices?.[0]?.delta?.content;
            if (typeof content === 'string') {
              omniContent += content;
            }
          }
        } catch (e) { /* ignore */ }
      }
      unlinkSync(audioFile.path);
      // 去除 markdown 包裹（如 ```json ... ``` 和 ```）
      omniContent = omniContent.replace(/```json|```/g, '');
      // 提取第一个 { 到最后一个 } 之间的内容，确保只保留 JSON 主体
      const firstBrace = omniContent.indexOf('{');
      const lastBrace = omniContent.lastIndexOf('}');
      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        omniContent = omniContent.substring(firstBrace, lastBrace + 1);
      }
      omniContent = omniContent.trim();
      let omniResult = null;
      try {
        omniResult = JSON.parse(omniContent);
      } catch (e) {
        throw new Error('Qwen-Omni 返回内容无法解析为评分 JSON');
      }
      // 保证返回结构统一，补充 transcript 字段（Qwen-Omni 不返回转写）
      omniResult.transcript = "（Qwen-Omni 评分不返回转写内容）";
      return res.json(omniResult);
    }

    // ...原 paraformer-v1 方案...
    const asrForm = new FormData();
    asrForm.append('model', 'paraformer-v1');
    asrForm.append('file', createReadStream(audioFile.path));
    const asrResponse = await fetch('https://dashscope.aliyuncs.com/api/v1/services/audio/asr/recognition', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
        ...asrForm.getHeaders()
      },
      body: asrForm
    });
    const asrData = await asrResponse.json();
    let userTranscript = '';
    if (asrData.output && asrData.output.transcription) {
      userTranscript = asrData.output.transcription.text;
    } else if (asrData.output && asrData.output.sentence_list) {
      userTranscript = asrData.output.sentence_list.map(s => s.text).join(' ');
    } else {
      throw new Error('语音识别失败: ' + (asrData.message || '未知错误'));
    }
    const prompt = `
      请扮演一位严格的英语口语老师。
      这是学生需要朗读的标准文本: "${standardTranscript}"
      这是学生实际朗读的录音转换成的文本: "${userTranscript}"

      请根据以下几点对学生的表现进行评分和评价：
      1.  **准确度 (Accuracy)**: 对比两个文本，计算匹配的单词比例，给出一个0-100的整数分数。
      2.  **完整度 (Completeness)**: 学生是否读完了所有内容，有无漏词或增词，给出一个0-100的整数分数。
      3.  **流利度 (Fluency)**: (这是一个简化的模拟) 如果文本几乎没有错误，可以认为是流利的。给出一个0-100的整数分数。
      4.  **反馈 (Feedback)**: 给出具体的中文反馈，指出哪些单词读错了，或者哪里有遗漏。

      请严格以JSON格式返回你的分析结果，不要包含任何额外说明。JSON对象必须包含 accuracy, completeness, fluency, 和 feedback 四个字段。
    `;

    const llmResponse = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`
      },
      body: JSON.stringify({
        model: 'qwen-turbo',
        input: { messages: [
          { role: 'system', content: 'You are a strict English speaking teacher.' },
          { role: 'user', content: prompt }
        ] },
        parameters: { result_format: 'json' }
      })
    });
    const llmData = await llmResponse.json();
    let scoreResult = null;
    if (llmData.output && llmData.output.choices && llmData.output.choices[0].message.content) {
      try {
        scoreResult = JSON.parse(llmData.output.choices[0].message.content);
      } catch (e) {
        throw new Error('AI评分返回内容无法解析为JSON');
      }
    } else {
      throw new Error('AI评分失败: ' + (llmData.message || '未知错误'));
    }
    unlinkSync(audioFile.path);
    res.json(scoreResult);
  } catch (error) {
    if (req.file && existsSync(req.file.path)) {
      unlinkSync(req.file.path);
    }
    res.status(500).json({ message: error.message || 'Failed to score the audio.' });
  }
});

app.get('/api/puzzles', async (req, res) => { /* Your original code here */ });
app.post('/api/puzzles', async (req, res) => { /* Your original code here */ });
app.get('/api/audio/:storyId', (req, res) => { /* Your original code here */ });
app.post('/api/chat', async (req, res) => {
  try {
    const { message, model } = req.body;
    if (!message) return res.status(400).json({ error: 'Missing message' });

    let useModel = model;
    if (!useModel || useModel === 'gemini-latest') useModel = 'gemini-1.5-flash';

    // --- Gemini 逻辑分支 (使用官方 SDK 重构) ---
    if (useModel.startsWith('gemini-')) {
      console.log(`--- [DEBUG] Entering Gemini SDK logic for model: ${useModel}`);

      const apiKey = process.env.GEMINI_API_KEY;
      if (!apiKey) {
        console.error('--- [FATAL DEBUG] GEMINI_API_KEY not found in .env file!');
        return res.status(500).json({ error: 'SERVER CONFIG ERROR: GEMINI_API_KEY is not set.' });
      }
      const ai = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });

      console.log(`--- [DEBUG] Sending message to Gemini via SDK: "${message}"`);

      const result = await ai.models.generateContent({
        model: useModel,
        contents: message
      });
      const text = result.text;
      console.log('--- [DEBUG] Full response from Gemini SDK:', JSON.stringify(result, null, 2));
      return res.json({ response: text, model: useModel });
    }

    // --- OpenRouter 逻辑分支 ---
    if (useModel.startsWith('openrouter/') || useModel === 'claude-3-haiku' || useModel === 'gpt-4o-mini') {
      console.log(`--- [DEBUG] Entering OpenRouter logic for model: ${useModel}`);

      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        console.error('--- [FATAL DEBUG] OPENROUTER_API_KEY not found in .env file!');
        return res.status(500).json({ error: 'SERVER CONFIG ERROR: OPENROUTER_API_KEY is not set.' });
      }

      console.log(`--- [DEBUG] Sending message to OpenRouter: "${message}"`);

      try {
        // 配置代理
        const proxyUrl = process.env.HTTPS_PROXY;
        let agent = undefined;
        if (proxyUrl) {
          const { HttpsProxyAgent } = await import('https-proxy-agent');
          agent = new HttpsProxyAgent(proxyUrl);
        }

        const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
          method: 'POST',
          agent: agent,
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:3001',
            'X-Title': 'English Learning Assistant'
          },
          body: JSON.stringify({
            model: useModel.startsWith('openrouter/') ? useModel.replace('openrouter/', '') : useModel,
            messages: [
              {
                role: 'system',
                content: 'You are an AI English learning assistant. Help users practice English conversation, correct grammar, and improve their language skills. Be encouraging and educational.'
              },
              {
                role: 'user',
                content: message
              }
            ],
            max_tokens: 1000,
            temperature: 0.7
          })
        });

        if (!response.ok) {
          throw new Error(`OpenRouter API failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        const text = result.choices[0]?.message?.content || '';

        console.log('--- [DEBUG] Response from OpenRouter:', text.substring(0, 100) + '...');
        return res.json({ response: text, model: useModel });

      } catch (error) {
        console.error('--- [FATAL OPENROUTER EXCEPTION]', error);
        return res.status(500).json({ error: `OpenRouter API error: ${error.message}` });
      }
    }

    // --- Qwen 逻辑分支 (阿里云通义千问) ---
    if (useModel === 'qwen-turbo' || useModel === 'qwen-omni-turbo') {
      console.log(`--- [DEBUG] Entering Qwen logic for model: ${useModel}`);

      const apiKey = process.env.DASHSCOPE_API_KEY;
      if (!apiKey) {
        console.error('--- [FATAL DEBUG] DASHSCOPE_API_KEY not found in .env file!');
        return res.status(500).json({ error: 'SERVER CONFIG ERROR: DASHSCOPE_API_KEY is not set.' });
      }

      console.log(`--- [DEBUG] Sending message to Qwen via API: "${message}"`);

      if (useModel === 'qwen-turbo') {
        // === qwen-turbo: 标准文本聊天模型 ===
        const qwenPayload = {
          model: useModel,
          input: {
            messages: [
              { role: 'system', content: 'You are a helpful English learning assistant.' },
              { role: 'user', content: message }
            ]
          },
          parameters: {
            result_format: 'message'
          }
        };

        // 阿里云API不使用代理（国内服务）
        let agent = undefined;

        const qwenResponse = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
          method: 'POST',
          agent: agent,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model: useModel,
            messages: [
              { role: 'system', content: 'You are a helpful English learning assistant.' },
              { role: 'user', content: message }
            ],
            stream: false
          })
        });

        if (!qwenResponse.ok) {
          const errorText = await qwenResponse.text();
          console.error(`--- [DEBUG] Qwen Turbo API error: ${qwenResponse.status} - ${errorText}`);
          throw new Error(`Qwen Turbo API failed: ${qwenResponse.status} - ${errorText}`);
        }

        const qwenData = await qwenResponse.json();
        const responseText = qwenData.choices?.[0]?.message?.content ||
                           '[Qwen Turbo无回复]';

        console.log(`--- [DEBUG] Qwen Turbo response: ${responseText}`);
        return res.json({ response: responseText, model: useModel });

      } else if (useModel === 'qwen-omni-turbo') {
        // === qwen-omni-turbo: 多模态聊天模型 ===
        const { image, audio, conversationHistory } = req.body;
        
        const messages = [
          { role: 'system', content: 'You are a helpful English learning assistant.' }
        ];
        
        // 添加对话历史
        if (conversationHistory && conversationHistory.length > 0) {
          messages.push(...conversationHistory);
        }
        
        // 构建用户消息
        const userMessage = {
          role: 'user',
          content: []
        };
        
        // 添加文本内容
        if (message) {
          userMessage.content.push({
            type: 'text',
            text: message
          });
        }
        
        // 添加图片内容
        if (image) {
          userMessage.content.push({
            type: 'image_url',
            image_url: { url: image }
          });
        }
        
        // 添加音频内容
        if (audio) {
          // 处理 base64 音频数据
          const audioData = audio.replace(/^data:audio\/[^;]+;base64,/, '');
          userMessage.content.push({
            type: 'input_audio',
            input_audio: { 
              data: `data:;base64,${audioData}`,
              format: 'wav'
            }
          });
        }
        
        messages.push(userMessage);
        
        const qwenPayload = {
          model: 'qwen-omni-turbo',
          messages: messages,
          modalities: ['text'],
          stream: true  // 添加这一行
        };

        const qwenResponse = await fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify(qwenPayload)
        });

        if (!qwenResponse.ok) {
          const errorText = await qwenResponse.text();
          console.error(`--- [DEBUG] Qwen Omni API error: ${qwenResponse.status} - ${errorText}`);
          throw new Error(`Qwen Omni API failed: ${qwenResponse.status} - ${errorText}`);
        }

        // 处理流式响应 - node-fetch 版本
        let responseText = '';
        
        if (!qwenResponse.body.readable) {
          // 如果不是流式响应，直接解析
          const qwenData = await qwenResponse.json();
          responseText = qwenData.choices?.[0]?.message?.content || '[Qwen Omni无回复]';
        } else {
          // 流式响应处理
          const decoder = new TextDecoder();
          
          for await (const chunk of qwenResponse.body) {
            const chunkStr = decoder.decode(chunk, { stream: true });
            const lines = chunkStr.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') continue;

                try {
                  const parsed = JSON.parse(data);
                  const delta = parsed.choices?.[0]?.delta?.content;
                  if (delta) {
                    responseText += delta;
                  }
                } catch (e) {
                  // 忽略解析错误的行
                }
              }
            }
          }
        }

        if (!responseText) {
          responseText = '[Qwen Omni无回复]';
        }

        console.log(`--- [DEBUG] Qwen Omni response: ${responseText}`);
        return res.json({ response: responseText, model: useModel });
      }
    }
  } catch (error) {
    console.error('--- [FATAL SDK EXCEPTION] An unexpected error occurred in /api/chat endpoint:');
    console.error(error); // 打印完整的错误对象
    res.status(500).json({ error: error.message || 'Chat接口发生未知异常' });
  }
});
// =========================================================================
// ===                     END OF THE MODIFIED CODE                      ===
// =========================================================================

app.delete('/api/stories/:id', async (req, res) => {
  try {
    const storyIdToDelete = req.params.id;
    console.log(`--- DELETE REQUEST for story ID: ${storyIdToDelete} ---`);

    const allStories = await getGeneratedStories();
    const updatedStories = allStories.filter(story => story.id !== storyIdToDelete);

    if (allStories.length === updatedStories.length) {
      console.log(`[DELETE] Story with ID ${storyIdToDelete} not found.`);
      return res.status(404).json({ message: 'Story not found' });
    }

    await fs.writeFile(generatedStoriesPath, JSON.stringify(updatedStories, null, 2));
    console.log(`[DELETE] Story with ID ${storyIdToDelete} has been deleted.`);
    res.status(200).json({ message: 'Story deleted successfully' });

  } catch (error) {
    console.error('[DELETE] Error deleting story:', error);
    res.status(500).json({ message: 'Failed to delete story' });
  }
});
app.post('/api/stories/batch-delete', async (req, res) => {
  try {
    const { ids } = req.body;
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: 'Please provide an array of story IDs to delete.' });
    }
    console.log(`--- BATCH DELETE REQUEST for story IDs: ${ids.join(', ')} ---`);
    const allStories = await getGeneratedStories();
    const updatedStories = allStories.filter(story => !ids.includes(story.id));
    await fs.writeFile(generatedStoriesPath, JSON.stringify(updatedStories, null, 2));
    console.log(`[BATCH DELETE] ${ids.length} stories have been deleted.`);
    res.status(200).json({ message: 'Stories deleted successfully' });
  } catch (error) {
    console.error('[BATCH DELETE] Error deleting stories:', error);
    res.status(500).json({ message: 'Failed to delete stories' });
  }
});
app.listen(port, () => {
  console.log(`Server listening at http://localhost:${port}`);
  console.log('GOOGLE_API_KEY:', process.env.GOOGLE_API_KEY);
  console.log('GEMINI_API_KEY:', process.env.GEMINI_API_KEY);
  console.log('HTTPS_PROXY:', process.env.HTTPS_PROXY);
});

// 在现有代码后添加 WebSocket 服务器
const wss = new WebSocketServer({ port: 3002 });

wss.on('connection', (ws) => {
  console.log('[Server] New WebSocket connection');
  let realtimeClient = null;

  ws.on('message', async (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log('[Server] Received WebSocket message:', data.type, data);
      
      // 自动启动会话（第一次连接时）
      if (!realtimeClient) {
        console.log('[Server] Auto-starting Qwen-Omni streaming session...');
        realtimeClient = new QwenRealtimeClient(process.env.DASHSCOPE_API_KEY);

        // 设置事件处理器
        realtimeClient.on('session.created', (event) => {
          console.log('[Server] Session created');
          ws.send(JSON.stringify({ type: 'session.created', event }));
        });

        realtimeClient.on('session.updated', (event) => {
          console.log('[Server] Session updated');
          ws.send(JSON.stringify({ type: 'session.updated', event }));
        });

        realtimeClient.on('response.created', (event) => {
          console.log('[Server] Response created');
          ws.send(JSON.stringify({ type: 'response.created', event }));
        });

        realtimeClient.on('response.text.delta', (event) => {
          // 发送文本增量
          ws.send(JSON.stringify({ type: 'response.text.delta', delta: event.delta }));
        });

        realtimeClient.on('response.audio.delta', (event) => {
          // 发送音频增量
          ws.send(JSON.stringify({ type: 'response.audio.delta', delta: event.delta }));
        });

        realtimeClient.on('response.audio.done', (event) => {
          console.log('[Server] Audio response complete, length:', event.audio ? event.audio.length : 0);
          ws.send(JSON.stringify({ type: 'response.audio.done', event }));
        });

        realtimeClient.on('response.done', (event) => {
          console.log('[Server] Response complete');
          ws.send(JSON.stringify({ type: 'response.done', event }));
        });

        realtimeClient.on('error', (event) => {
          console.error('[Server] Streaming error:', event);
          ws.send(JSON.stringify({ type: 'error', error: event }));
        });

        // 连接到阿里云API
        try {
          await realtimeClient.connect();
          console.log('[Server] Qwen-Omni streaming client connected successfully');
        } catch (error) {
          console.error('[Server] Failed to connect streaming client:', error);
          ws.send(JSON.stringify({ type: 'error', error: { message: 'Failed to connect to Qwen-Omni API: ' + error.message } }));
          return;
        }
      }
      
      // 检查是否已连接到阿里云
      if (!realtimeClient || !realtimeClient.isConnected) {
        console.log('[Server] Waiting for realtime connection, queuing message:', data.type);
        // 发送等待状态给前端
        ws.send(JSON.stringify({ 
          type: 'connection.pending', 
          message: 'Waiting for realtime connection...' 
        }));
        return;
      }
      
      if (data.type === 'conversation.item.create' && realtimeClient) {
        // 处理对话项创建（文本/图片消息）
        console.log('[Server] Creating conversation item');
        try {
          if (data.item && data.item.content) {
            // 提取文本和图片内容
            const textContent = data.item.content.find(c => c.type === 'input_text');
            const imageContent = data.item.content.find(c => c.type === 'input_image');

            if (textContent && textContent.text) {
              if (imageContent && imageContent.image) {
                // 处理图片，提取base64数据
                let imageBase64 = imageContent.image;
                if (imageBase64.startsWith('data:')) {
                  imageBase64 = imageBase64.split(',')[1];
                }
                await realtimeClient.sendMultimodalMessage(textContent.text, imageBase64);
              } else {
                await realtimeClient.sendTextMessage(textContent.text);
              }
            }
          }
        } catch (error) {
          console.error('[Server] Error creating conversation item:', error);
          ws.send(JSON.stringify({ type: 'error', error: { message: 'Failed to create conversation item: ' + error.message } }));
        }

      } else if (data.type === 'input_audio_buffer.append' && realtimeClient) {
        // 处理音频块追加
        console.log('[Server] Appending audio buffer');
        try {
          await realtimeClient.sendAudioChunk(data.audio);
        } catch (error) {
          console.error('[Server] Error appending audio buffer:', error);
          ws.send(JSON.stringify({ type: 'error', error: { message: 'Failed to append audio buffer: ' + error.message } }));
        }

      } else if (data.type === 'input_audio_buffer.commit' && realtimeClient) {
        // 处理音频提交
        console.log('[Server] Committing audio buffer');
        try {
          await realtimeClient.commitAudio();
        } catch (error) {
          console.error('[Server] Error committing audio buffer:', error);
          ws.send(JSON.stringify({ type: 'error', error: { message: 'Failed to commit audio buffer: ' + error.message } }));
        }

      } else if (data.type === 'response.create' && realtimeClient) {
        // 响应创建在sendTextMessage中自动处理
        console.log('[Server] Response create event (handled automatically)');

      } else {
        console.log('[Server] Unhandled message type:', data.type);
      }
      
    } catch (error) {
      console.error('[Server] WebSocket message error:', error);
      ws.send(JSON.stringify({ type: 'error', error: { message: error.message } }));
    }
  });

  ws.on('close', () => {
    console.log('[Server] WebSocket disconnected');
    if (realtimeClient) {
      realtimeClient.disconnect();
    }
  });

  ws.on('error', (error) => {
    console.error('[Server] WebSocket error:', error);
    if (realtimeClient) {
      realtimeClient.disconnect();
    }
  });
});

console.log('WebSocket server running on port 3002');

// 专门的 TTS 路由
app.post('/api/tts', async (req, res) => {
  try {
    const { text, model = 'qwen-tts', voice = 'Serena' } = req.body;
    
    if (!text || text.trim() === '') {
      return res.status(400).json({ error: 'Text is required' });
    }

    const apiKey = process.env.DASHSCOPE_API_KEY;
    if (!apiKey) {
      return res.status(500).json({ error: 'DASHSCOPE_API_KEY not configured' });
    }

    console.log(`[TTS API] Generating audio for text: ${text.substring(0, 100)}...`);
    
    // 调用现有的 generateAudio 函数
    const audioUrl = await generateAudio(text, apiKey);
    
    if (!audioUrl) {
      throw new Error('Failed to generate audio');
    }
    
    console.log(`[TTS API] Audio generated successfully: ${audioUrl}`);
    res.json({ audioUrl });
    
  } catch (error) {
    console.error('[TTS API] Error:', error.message);
    res.status(500).json({ 
      error: 'Failed to generate audio',
      details: error.message 
    });
  }
});