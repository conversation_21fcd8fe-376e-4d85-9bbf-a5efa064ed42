import React from 'react';
import { ChatHistoryItem } from '@/hooks/useAIChat';
import { Trash2 } from 'lucide-react';

interface SidebarProps {
  onNewChat: () => void;
  chatHistory: ChatHistoryItem[];
  onSelectChat: (id: string) => void;
  currentChatId: string | null;
  onDeleteChat: (id: string) => void;
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ onNewChat, chatHistory, onSelectChat, currentChatId, onDeleteChat, className }) => {
  return (
    <div className="flex flex-col h-full p-4 bg-gray-100 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
      <button
        onClick={onNewChat}
        className="w-full px-4 py-2 mb-4 text-left text-white bg-blue-500 rounded-md hover:bg-blue-600 transition-colors"
      >
        + New Chat
      </button>
      <div className="flex-grow overflow-y-auto">
        <h2 className="text-lg font-semibold mb-2 text-gray-800 dark:text-gray-200">History</h2>
        {chatHistory.length > 0 ? (
          <ul>
            {chatHistory.map((item) => (
              <li key={item.id} className="mb-1 group flex items-center justify-between rounded-md hover:bg-gray-200 dark:hover:bg-gray-700">
                <button
                  onClick={() => onSelectChat(item.id)}
                  className={`flex-1 text-left p-2 rounded-md truncate transition-colors ${
                    item.id === currentChatId
                      ? 'font-semibold'
                      : ''
                  }`}
                >
                  {item.title}
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteChat(item.id);
                  }}
                  className="p-1 mr-1 rounded-md opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 transition-opacity"
                  aria-label={`Delete chat ${item.title}`}
                >
                  <Trash2 size={16} />
                </button>
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-sm text-gray-500 dark:text-gray-400">
                        No conversations yet.
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
