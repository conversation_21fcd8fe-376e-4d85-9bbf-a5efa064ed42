// 媒体设备安全检测工具
export const mediaDevicesUtil = {
  // 检查是否支持媒体设备API
  isSupported(): boolean {
    try {
      return !!(
        navigator && 
        navigator.mediaDevices && 
        typeof navigator.mediaDevices.getUserMedia === 'function'
      );
    } catch (error) {
      console.warn('Media devices check failed:', error);
      return false;
    }
  },

  // 检查是否在安全上下文中
  isSecureContext(): boolean {
    try {
      return (
        window.isSecureContext ||
        window.location.protocol === 'https:' ||
        window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1'
      );
    } catch (error) {
      console.warn('Secure context check failed:', error);
      return false;
    }
  },

  // 安全地请求用户媒体权限
  async requestUserMedia(constraints: MediaStreamConstraints): Promise<MediaStream | null> {
    try {
      if (!this.isSupported()) {
        throw new Error('Media devices not supported');
      }

      if (!this.isSecureContext()) {
        throw new Error('Media access requires secure context (HTTPS)');
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      return stream;
    } catch (error) {
      console.error('getUserMedia failed:', error);
      
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          throw new Error('用户拒绝了麦克风权限');
        } else if (error.name === 'NotFoundError') {
          throw new Error('未找到可用的麦克风设备');
        } else if (error.name === 'NotSupportedError') {
          throw new Error('浏览器不支持音频录制');
        } else if (error.name === 'SecurityError') {
          throw new Error('需要在安全环境（HTTPS）下使用麦克风');
        }
      }
      
      throw error;
    }
  },

  // 获取用户友好的错误信息
  getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    return '媒体设备访问失败';
  }
};
