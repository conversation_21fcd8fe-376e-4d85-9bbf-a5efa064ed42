const https = require('https');

const data = JSON.stringify({
  model: 'qwen-omni-turbo',
  messages: [
    {
      role: 'user',
      content: [
        {
          type: 'text',
          text: 'Hello, how are you?'
        }
      ]
    }
  ],
  modalities: ['text'],
  stream: true // 改这里，从 false 改成 true
});

const options = {
  hostname: 'dashscope.aliyuncs.com',
  port: 443,
  path: '/compatible-mode/v1/chat/completions',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer sk-3207773e69984ecf9ca3d452fa6d4661',
    'Content-Length': data.length
  }
};

const req = https.request(options, (res) => {
  console.log(`statusCode: ${res.statusCode}`);
  console.log(`headers:`, res.headers);

  res.on('data', (d) => {
    process.stdout.write(d);
  });
});

req.on('error', (error) => {
  console.error(error);
});

req.write(data);
req.end();