import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Target,
  BarChart3,
  LineChart,
  Maximize2,
  TrendingUp,
  Timer,
  Globe,
  BookOpen,
  Mic,
  PenSquare,
  Headphones
} from 'lucide-react';
import { Progress } from "@/components/ui/progress";
import React from 'react';

interface Skill {
  name: string;
  icon: any;
  score: number;
  improvement: number;
  testCount: number;
}

const skills: Skill[] = [
  {
    name: '听力理解',
    icon: Headphones,
    score: 85,
    improvement: 5,
    testCount: 12
  },
  {
    name: '口语表达',
    icon: Mic,
    score: 75,
    improvement: 8,
    testCount: 10
  },
  {
    name: '阅读理解',
    icon: BookOpen,
    score: 90,
    improvement: 3,
    testCount: 15
  },
  {
    name: '写作技能',
    icon: PenSquare,
    score: 70,
    improvement: 10,
    testCount: 8
  },
  {
    name: '词汇量',
    icon: Globe,
    score: 82,
    improvement: 6,
    testCount: 20
  }
];

const Analysis = () => {
  const [selectedSkill, setSelectedSkill] = useState<Skill | null>(null);
  const [analysis, setAnalysis] = useState('');
  const [loading, setLoading] = useState(false);
  const [timeSpent] = useState({
    total: 48,
    listening: 12,
    speaking: 10,
    reading: 15,
    writing: 11
  });

  const getAnalysis = async (skill: Skill) => {
    setSelectedSkill(skill);
    setLoading(true);
    try {
      const { data, error } = await supabase.functions.invoke('chat-ai', {
        body: {
          message: `请根据以下数据分析学习情况并给出建议：
技能：${skill.name}
当前得分：${skill.score}
进步幅度：${skill.improvement}
练习次数：${skill.testCount}
请给出具体的学习建议和提升方法。`,
          conversationHistory: [],
          model: 'gemini-2.0-flash-exp',
        },
      });
      if (error) throw error;
      setAnalysis(data.response);
    } catch (err: any) {
      setAnalysis('AI服务出错，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8 text-center">个性化分析</h1>

      {/* 总览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50">
          <div className="flex items-center gap-4 mb-2">
            <div className="p-3 bg-blue-100 rounded-lg">
              <Target className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium">总体得分</h3>
              <p className="text-2xl font-bold text-blue-600">82</p>
            </div>
          </div>
          <Progress value={82} className="h-2" />
        </Card>

        <Card className="p-6 bg-gradient-to-br from-emerald-50 to-green-50">
          <div className="flex items-center gap-4 mb-2">
            <div className="p-3 bg-emerald-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-emerald-600" />
            </div>
            <div>
              <h3 className="font-medium">本月进步</h3>
              <p className="text-2xl font-bold text-emerald-600">+6.5%</p>
            </div>
          </div>
          <Progress value={65} className="h-2" />
        </Card>

        <Card className="p-6 bg-gradient-to-br from-amber-50 to-yellow-50">
          <div className="flex items-center gap-4 mb-2">
            <div className="p-3 bg-amber-100 rounded-lg">
              <Timer className="w-6 h-6 text-amber-600" />
            </div>
            <div>
              <h3 className="font-medium">学习时长</h3>
              <p className="text-2xl font-bold text-amber-600">{timeSpent.total}h</p>
            </div>
          </div>
          <div className="grid grid-cols-4 gap-2 mt-4">
            <div className="text-center">
              <div className="text-sm text-gray-500">听力</div>
              <div className="font-medium">{timeSpent.listening}h</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">口语</div>
              <div className="font-medium">{timeSpent.speaking}h</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">阅读</div>
              <div className="font-medium">{timeSpent.reading}h</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">写作</div>
              <div className="font-medium">{timeSpent.writing}h</div>
            </div>
          </div>
        </Card>
      </div>

      {/* 技能分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            能力评估
          </h2>
          <div className="space-y-6">
            {skills.map((skill) => {
              const Icon = skill.icon;
              return (
                <div
                  key={skill.name}
                  className={`p-4 rounded-lg cursor-pointer transition-colors ${
                    selectedSkill?.name === skill.name
                      ? 'bg-gray-100'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => getAnalysis(skill)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <Icon className="w-5 h-5 text-gray-600" />
                      <span className="font-medium">{skill.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-500">
                        {skill.score}分
                      </span>
                      <span className="text-xs text-emerald-600">
                        +{skill.improvement}%
                      </span>
                    </div>
                  </div>
                  <Progress value={skill.score} className="h-2" />
                  <div className="mt-2 text-sm text-gray-500">
                    已完成 {skill.testCount} 次测试
                  </div>
                </div>
              );
            })}
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-6 flex items-center gap-2">
            <LineChart className="w-5 h-5" />
            学习建议
          </h2>
          
          {selectedSkill ? (
            <div>
              <div className="flex items-center gap-4 mb-6">
                <div className="p-3 bg-gray-100 rounded-lg">
                  {React.createElement(selectedSkill.icon, {
                    className: "w-6 h-6 text-gray-600"
                  })}
                </div>
                <div>
                  <h3 className="font-medium">{selectedSkill.name}</h3>
                  <p className="text-sm text-gray-500">
                    当前得分：{selectedSkill.score}分 · 进步：+{selectedSkill.improvement}%
                  </p>
                </div>
              </div>

              {loading ? (
                <div className="text-center py-8">分析中...</div>
              ) : (
                <div className="prose max-w-none">
                  {analysis.split('\n').map((line, i) => (
                    <p key={i}>{line}</p>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              ← 请选择一个技能查看详细分析
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

export default Analysis;
