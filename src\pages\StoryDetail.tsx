import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Play, Download, ArrowLeft, Loader2, Pause, Volume2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useMutation } from '@tanstack/react-query';
import ClickableText from '@/components/ClickableText';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { type Story } from '@/types/story';

interface WordDefinition {
  word: string;
  definition: string;
  partOfSpeech: string;
  example: string;
  phonetic: string;
  chineseDefinition: string;
  chineseExample: string;
  baseForm: string;
}

const StoryDetail = () => {
  const { id: storyId } = useParams<{ id: string }>();
  const { toast } = useToast();
  
  const [story, setStory] = useState<Story | null>(null);
  const [isLoadingStory, setIsLoadingStory] = useState(true);
  const [isGeneratingAudio, setIsGeneratingAudio] = useState(false);

  const [definition, setDefinition] = useState<WordDefinition | null>(null);
  const [isDefinitionOpen, setIsDefinitionOpen] = useState(false);
  const [definitionIsLoading, setDefinitionIsLoading] = useState(false);
  
  const handleAudioEnd = useCallback(() => {
    toast({
      title: "Playback Finished",
      description: "The story reading has ended."
    });
  }, [toast]);

  const { isPlaying, isLoading: isAudioLoading, play, pause } = useAudioPlayer({
    onEnd: handleAudioEnd
  });

  useEffect(() => {
    const fetchStory = async () => {
      if (!storyId) {
        setIsLoadingStory(false);
        return;
      }
      try {
        setIsLoadingStory(true);
        const response = await fetch(`/api/stories/${storyId}`);
        if (!response.ok) {
          throw new Error('Story not found');
        }
        const data: Story = await response.json();
        setStory(data);
      } catch (error) {
        console.error('Failed to fetch story:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Could not load the story.",
        });
      } finally {
        setIsLoadingStory(false);
      }
    };

    fetchStory();
  }, [storyId, toast]);

  const generateAndPlayAudio = async () => {
    if (!story || !story.id || !story.story) return;

    setIsGeneratingAudio(true);
    try {
      const response = await fetch('/api/audio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ storyId: story.id, text: story.story }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate audio.');
      }

      const updatedStory: Story = await response.json();
      setStory(updatedStory); // Update story state with new audioUrl
      if (updatedStory.audioUrl) {
        play(updatedStory.audioUrl);
      }
    } catch (error) {
      console.error('Audio generation failed:', error);
      toast({
        variant: "destructive",
        title: "Audio Generation Failed",
        description: error instanceof Error ? error.message : "Please try again later.",
      });
    } finally {
      setIsGeneratingAudio(false);
    }
  };

  // Query word definition
  const getWordDefinitionMutation = useMutation({
    mutationFn: async ({ word, context }: { word: string; context: string }) => {
      const { data, error } = await supabase.functions.invoke('get-word-definition', {
        body: { word, context }
      });

      if (error) throw error;
      return data as WordDefinition;
    },
    onSuccess: (data) => {
      setDefinition(data);
      setDefinitionIsLoading(false);
      setIsDefinitionOpen(true);
    },
    onError: (error) => {
      console.error('获取单词释义失败:', error);
      toast({
        variant: "destructive",
        title: "获取释义失败",
        description: error instanceof Error ? error.message : "请重试"
      });
      setDefinitionIsLoading(false);
    }
  });

  const handlePlayClick = useCallback(async () => {
    if (!story) return;

    if (isPlaying) {
      pause();
    } else if (story.audioUrl) {
      play(story.audioUrl);
    } else {
      await generateAndPlayAudio();
    }
  }, [isPlaying, story, play, pause]);

  const handleDownload = () => {
    if (!story?.audioUrl) {
      toast({
        variant: "destructive",
        title: "Download Failed",
        description: "Audio file does not exist.",
      });
      return;
    }

    const a = document.createElement('a');
    a.href = story.audioUrl;
    a.download = `${story?.title || 'story'}.mp3`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  if (isLoadingStory) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin" />
        <p className="mt-2">Loading story...</p>
      </div>
    );
  }

  if (!story) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <p>Story not found.</p>
        <Link to="/stories">
          <Button>Back to Stories</Button>
        </Link>
      </div>
    );
  }

  const isPlayButtonDisabled = !story.story || isGeneratingAudio || isAudioLoading;
  const showPlayIcon = !isPlaying && !isAudioLoading && !isGeneratingAudio;

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center">
            <Link to="/stories" className="mr-4">
              <Button variant="ghost" size="icon">
                <ArrowLeft />
              </Button>
            </Link>
            {story.title}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={handlePlayClick} disabled={isPlayButtonDisabled}>
              {isGeneratingAudio || isAudioLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : story.audioUrl ? (
                <Play className="h-4 w-4" />
              ) : (
                <Volume2 className="h-4 w-4" /> // Icon to prompt generation
              )}
            </Button>
            <Button variant="outline" size="icon" onClick={handleDownload} disabled={!story.audioUrl}>
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {story.imageUrl && (
            <img src={story.imageUrl} alt={story.title} className="mb-4 max-h-96 w-full rounded-lg object-contain" />
          )}
          <div className="mt-4 text-lg leading-relaxed">
            <div className="whitespace-pre-wrap rounded-md border p-4">
              <ClickableText text={story.story || ''} onWordClick={(word) => getWordDefinitionMutation.mutate({ word, context: story.story || '' })} />
            </div>
          </div>
          {story.storyChinese && (
            <div className="mt-4">
              <h3 className="text-lg font-semibold">中文翻译</h3>
              <div className="whitespace-pre-wrap rounded-md border bg-gray-50 p-4 text-base">
                {story.storyChinese}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isDefinitionOpen} onOpenChange={setIsDefinitionOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Word Definition</DialogTitle>
          </DialogHeader>
          {definitionIsLoading ? (
            <div className="flex justify-center items-center h-24">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : definition ? (
            <div>
              <h3 className="text-xl font-bold">{definition.word} <span className="text-base font-normal text-muted-foreground">{definition.phonetic}</span></h3>
              <p className="mt-2"><strong>Part of Speech:</strong> {definition.partOfSpeech}</p>
              <p className="mt-1"><strong>Definition:</strong> {definition.definition}</p>
              <p className="mt-1"><em>{definition.example}</em></p>
              <hr className="my-2" />
              <p className="mt-1"><strong>中文释义:</strong> {definition.chineseDefinition}</p>
              <p className="mt-1"><em>{definition.chineseExample}</em></p>
            </div>
          ) : (
            <p>No definition found.</p>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StoryDetail;
