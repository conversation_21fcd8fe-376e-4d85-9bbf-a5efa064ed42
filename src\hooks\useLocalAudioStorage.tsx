import { useCallback } from 'react';
import type { LocalStory } from '@/types/story';

// Helper to convert Blob to Base64 string
const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

// Helper to convert Base64 string back to Blob using the fetch API for robustness
const base64ToBlob = async (base64: string): Promise<Blob> => {
  const response = await fetch(base64);
  const blob = await response.blob();
  return blob;
};

const AUDIO_PREFIX = 'audio_';
const getAudioKey = (storyId: string) => `${AUDIO_PREFIX}${storyId}`;

// This is a simplified interface for the localStorage workaround.
// The full AudioData interface is not used to avoid performance issues.
export interface AudioData {
  id: string;
  title: string;
  blob: Blob;
  size: number;
  timestamp: number;
}

const AUDIO_KEY_PREFIX = 'audio_';

// Helper function to get all audio storage keys
const getAudioStorageKeys = () => {
  const keys = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith(AUDIO_KEY_PREFIX)) {
      keys.push(key);
    }
  }
  return keys;
};


export const useLocalAudioStorage = () => {
  const getLocalStories = useCallback(() => {
    try {
      const storiesJSON = localStorage.getItem('local_stories');
      return storiesJSON ? JSON.parse(storiesJSON) as LocalStory[] : [];
    } catch (error) {
      console.error('获取本地故事失败:', error);
      return [];
    }
  }, []);

  const updateStoryAudioStatus = useCallback((storyId: string, hasAudio: boolean) => {
    try {
      const stories = getLocalStories();
      const storyIndex = stories.findIndex(s => s.id === storyId);
      if (storyIndex !== -1) {
        stories[storyIndex].hasAudio = hasAudio;
        localStorage.setItem('local_stories', JSON.stringify(stories));
      }
    } catch (error) {
      console.error('更新故事音频状态失败:', error);
    }
  }, [getLocalStories]);

  const cleanupOldAudio = useCallback(async (bytesNeeded: number) => {
    console.log(`需要释放 ${bytesNeeded} 字节空间。开始清理...`);
    let bytesFreed = 0;

    try {
      const stories = getLocalStories();
      // Sort stories by lastAccessed date, oldest first.
      const sortedStories = stories.sort((a, b) => 
        new Date(a.lastAccessed).getTime() - new Date(b.lastAccessed).getTime()
      );

      for (const story of sortedStories) {
        if (story.hasAudio) {
          const key = `${AUDIO_KEY_PREFIX}${story.id}`;
          const item = localStorage.getItem(key);
          if (item) {
            const itemSize = new TextEncoder().encode(item).length;
            localStorage.removeItem(key);
            updateStoryAudioStatus(story.id, false);
            bytesFreed += itemSize;
            console.log(`已删除旧音频: ${story.title} (释放 ${itemSize} 字节)`);
            if (bytesFreed >= bytesNeeded) {
              console.log(`成功释放 ${bytesFreed} 字节空间。`);
              return true; // Space freed
            }
          }
        }
      }
    } catch (error) {
      console.error('清理旧音频时出错:', error);
    }

    if (bytesFreed > 0) {
      console.log(`总共释放了 ${bytesFreed} 字节, 但可能仍然不够。`);
      return true;
    }

    console.warn('没有可清理的旧音频来释放空间。');
    return false; // No space could be freed
  }, [getLocalStories, updateStoryAudioStatus]);


  const saveAudioBlob = useCallback(async (storyId: string, blob: Blob) => {
    const base64Audio = await new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });

    const key = `${AUDIO_KEY_PREFIX}${storyId}`;
    
    try {
      localStorage.setItem(key, base64Audio);
    } catch (e: any) {
      if (e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED') {
        console.warn('Quota exceeded. Attempting to free up space...');
        const audioSize = new TextEncoder().encode(base64Audio).length;
        const spaceFreed = await cleanupOldAudio(audioSize);

        if (spaceFreed) {
          try {
            // Retry saving
            localStorage.setItem(key, base64Audio);
            console.log('成功保存音频 after cleanup.');
          } catch (retryError: any) {
            console.error('重试保存音频失败:', retryError);
            throw new Error('清理空间后，保存音频仍然失败。');
          }
        } else {
          console.error('无法释放足够的空间来保存音频。');
          throw new Error('音频存储空间不足，且无法自动清理。');
        }
      } else {
        console.error('保存音频到localStorage时发生未知错误:', e);
        throw e;
      }
    }
  }, [cleanupOldAudio]);

  const getAudioBlob = useCallback(async (storyId: string): Promise<Blob | null> => {
    try {
      const base64Audio = localStorage.getItem(getAudioKey(storyId));
      if (base64Audio) {
        console.log(`Audio for story ${storyId} retrieved from localStorage.`);
        // Directly use the base64 string
        return await base64ToBlob(base64Audio);
      }
      return null;
    } catch (error) {
      console.error('Failed to retrieve audio from localStorage:', error);
      return null;
    }
  }, []);

  const deleteAudio = useCallback((storyId: string) => {
    try {
      const key = `${AUDIO_KEY_PREFIX}${storyId}`;
      localStorage.removeItem(key);
    } catch (error) {
      console.error(`删除音频失败 storyId=${storyId}:`, error);
    }
  }, []);

  const getStorageUsage = useCallback(() => {
    let totalSize = 0;
    const audioKeys = getAudioStorageKeys();
    audioKeys.forEach(key => {
      const item = localStorage.getItem(key);
      if (item) {
        totalSize += new TextEncoder().encode(item).length;
      }
    });
    return totalSize;
  }, []);

  return { saveAudioBlob, getAudioBlob, deleteAudio, getStorageUsage, cleanupOldAudio };
};
