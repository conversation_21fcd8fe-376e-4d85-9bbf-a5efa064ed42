import React from 'react';

export interface ClickableTextProps {
  text: string;
  onWordClick: (word: string, context: string) => void;
  className?: string;
}

const ClickableText: React.FC<ClickableTextProps> = ({ text, onWordClick, className }) => {
  const handleWordClick = (e: React.MouseEvent<HTMLSpanElement>) => {
    const word = e.currentTarget.textContent?.trim();
    if (!word) return;

    // Get surrounding context (previous and next words)
    const words = text.split(/\s+/);
    const clickedIndex = words.findIndex(w => w.includes(word));
    const start = Math.max(0, clickedIndex - 3);
    const end = Math.min(words.length, clickedIndex + 4);
    const context = words.slice(start, end).join(' ');

    onWordClick(word, context);
  };

  return (
    <p className={className}>
      {text.split(/\b/).map((part, index) => {
        // Skip spaces and punctuation
        if (/^\s*$|^[.,!?;:'")\]}]+$/.test(part)) {
          return part;
        }
        
        return (
          <span
            key={index}
            className="cursor-pointer hover:bg-yellow-100 transition-colors duration-200"
            onClick={handleWordClick}
          >
            {part}
          </span>
        );
      })}
    </p>
  );
};

export default ClickableText;
