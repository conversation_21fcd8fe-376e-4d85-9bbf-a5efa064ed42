import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// 增强的错误处理 - 防止第三方脚本干扰
const originalConsoleError = console.error;
console.error = (...args) => {
  const message = args.join(' ');
  
  // 更全面地过滤第三方扩展的错误
  if (
    (message.includes('getUserMedia') || 
     message.includes('mediaDevices') || 
     message.includes('navigator.mediaDevices')) && 
    (message.includes('content.js') || 
     message.includes('content-all.js') || 
     message.includes('extension') ||
     message.includes('chrome-extension'))
  ) {
    // 完全静默处理第三方扩展错误
    return;
  }
  
  // 过滤所有来自content.js的错误
  if (message.includes('content.js')) {
    return;
  }
  
  // 对于网络超时错误，提供更友好的提示
  if (message.includes('请求超时') || message.includes('网络连接')) {
    originalConsoleError.apply(console, ['🌐 网络问题:', ...args]);
    return;
  }
  
  // 对于故事生成失败，添加更友好的错误信息
  if (message.includes('故事生成失败')) {
    originalConsoleError.apply(console, ['❌ 故事生成遇到问题，请检查配置:', ...args]);
    return;
  }
  
  // 其他错误正常显示
  originalConsoleError.apply(console, args);
};

// 保护 navigator.mediaDevices
if (navigator.mediaDevices) {
  const originalGetUserMedia = navigator.mediaDevices.getUserMedia;
  navigator.mediaDevices.getUserMedia = function(constraints) {
    try {
      return originalGetUserMedia.call(this, constraints);
    } catch (error) {
      console.error('MediaDevices error:', error);
      throw error;
    }
  };
}

createRoot(document.getElementById("root")!).render(<App />);
