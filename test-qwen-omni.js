// 测试阿里云通义千问 Qwen-Omni API
import dotenv from 'dotenv';
dotenv.config();

const apiKey = process.env.DASHSCOPE_API_KEY;

if (!apiKey) {
  console.error('DASHSCOPE_API_KEY not found in environment variables');
  process.exit(1);
}

console.log('Testing Qwen-Omni API...');

fetch('https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`
  },
  body: JSON.stringify({
    model: 'qwen-omni-turbo',
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'Hello, how are you? Please explain AI in simple terms.'
          }
        ]
      }
    ],
    modalities: ['text'],
    stream: false
  })
})
  .then(res => {
    console.log('Response status:', res.status);
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }
    return res.json();
  })
  .then(data => {
    console.log('Success response:', JSON.stringify(data, null, 2));
  })
  .catch(error => {
    console.error('Error:', error);
  });