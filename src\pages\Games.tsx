import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Gamepad2,
  Trophy,
  Target,
  Puzzle as PuzzleIcon, // Renamed to avoid conflict
  Sparkles,
  PlayCircle,
  Timer,
  Star,
  Medal,
  Crown,
} from 'lucide-react';
import { Progress } from "@/components/ui/progress";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import ConfigManager from '@/components/ConfigManager';
import EnvironmentChecker from '@/components/EnvironmentChecker';
import WordPuzzle from '@/components/games/WordPuzzle';
import { AIWordPuzzleGenerator } from '@/components/games/AIWordPuzzleGenerator'; // Import the new component

interface Game {
  id: string;
  icon: any;
  title: string;
  description: string;
  level: string;
  points: number;
  category: string;
  component?: React.ReactNode; // Optional component for the game
}

const games: Game[] = [
  {
    id: 'word-puzzle',
    icon: PuzzleIcon,
    title: '单词拼图',
    description: '通过图片和提示猜单词，学习新词汇。',
    level: '初级',
    points: 100,
    category: '词汇',
    component: <WordPuzzle onBack={() => {}} /> // Placeholder onBack
  },
  {
    id: 'ai-word-puzzle',
    icon: Sparkles,
    title: 'AI单词生成器',
    description: '输入主题，让AI为你创建独一无二的单词谜题。',
    level: '动态',
    points: 150,
    category: 'AI & 词汇',
    component: <AIWordPuzzleGenerator />
  },
  {
    id: 'sentence-match',
    icon: Target,
    title: '句子匹配',
    description: '将英文句子与中文含义正确匹配',
    level: '中级',
    points: 150,
    category: '语法'
  },
  {
    id: 'audio-quest',
    icon: Sparkles,
    title: '听力冒险',
    description: '听音频回答问题，探索英语世界',
    level: '高级',
    points: 200,
    category: '听力'
  },
  {
    id: 'grammar-challenge',
    icon: Target,
    title: '语法挑战',
    description: '在限定时间内修正句子中的语法错误',
    level: '中级',
    points: 150,
    category: '语法'
  },
  {
    id: 'pronunciation-master',
    icon: Sparkles,
    title: '发音大师',
    description: '跟读并模仿标准发音，获得实时反馈',
    level: '所有水平',
    points: 250,
    category: '发音'
  },
  {
    id: 'role-play-scenarios',
    icon: PuzzleIcon,
    title: '情景对话',
    description: '参与模拟对话，如在餐厅点餐或问路',
    level: '初级',
    points: 120,
    category: '口语'
  }
];

const Games = () => {
  const [activeGameId, setActiveGameId] = useState<string | null>(null);

  const handleGameSelect = (game: Game) => {
    if (game.component) {
      setActiveGameId(game.id);
    } else {
      // Handle games without a direct component (e.g., show a coming soon message)
      alert(`'${game.title}' is coming soon!`);
    }
  };

  const handleBackToLobby = () => {
    setActiveGameId(null);
  };

  const activeGame = games.find(g => g.id === activeGameId);

  if (activeGame && activeGame.component) {
    // To pass the onBack function, we need to clone the element
    return React.cloneElement(activeGame.component as React.ReactElement<any>, { onBack: handleBackToLobby });
  }

  return (
    <div className="container mx-auto p-4">
      <header className="text-center mb-8">
        <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-gray-100 sm:text-5xl md:text-6xl">
          <span className="inline-block bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 text-transparent bg-clip-text">
            游戏化学习中心
          </span>
        </h1>
        <p className="mt-3 max-w-md mx-auto text-base text-gray-500 dark:text-gray-400 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
          在游戏中提升你的英语技能！完成挑战，赢取积分，成为英语大师。
        </p>
      </header>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {games.map((game) => (
          <Card 
            key={game.id} 
            className="flex flex-col cursor-pointer hover:shadow-lg hover:border-primary transition-all duration-300"
            onClick={() => handleGameSelect(game)}
          >
            <div className="p-6 flex-grow">
              <div className="flex items-center mb-4">
                <game.icon className="w-8 h-8 mr-4 text-primary" />
                <h2 className="text-2xl font-bold">{game.title}</h2>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-4">{game.description}</p>
            </div>
            <div className="p-6 bg-gray-50 dark:bg-gray-800/50 border-t flex justify-between items-center">
              <span className="text-sm font-semibold text-gray-500">{game.category} - {game.level}</span>
              <span className="font-bold text-yellow-500">✨ {game.points} pts</span>
            </div>
          </Card>
        ))}
      </div>

      <div className="mt-12">
        <Accordion type="single" collapsible className="w-full">
          <AccordionItem value="item-1">
            <AccordionTrigger>
                <h3 className="text-xl font-semibold">环境与配置</h3>
            </AccordionTrigger>
            <AccordionContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-4">
                    <EnvironmentChecker />
                    <ConfigManager />
                </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <footer className="text-center mt-12 text-gray-500">
        <p>持续推出更多好玩的游戏，敬请期待！</p>
      </footer>
    </div>
  );
};

export default Games;
