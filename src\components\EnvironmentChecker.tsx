import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Info,
  RefreshCw,
  Shield,
  Mic,
  Volume2
} from 'lucide-react';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";

// 简化的环境检查函数，避免循环依赖
const checkSimpleEnvironment = () => {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // 检查安全上下文
  const isSecureContext = 
    window.isSecureContext || 
    window.location.protocol === 'https:' || 
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1';

  if (!isSecureContext) {
    issues.push('需要安全环境（HTTPS）才能使用麦克风功能');
    recommendations.push('请使用 HTTPS 协议访问，或在 localhost 环境下测试');
  }

  // 检查媒体设备API
  const hasMediaDevices = !!navigator.mediaDevices;
  const hasGetUserMedia = hasMediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function';

  if (!hasMediaDevices) {
    issues.push('浏览器不支持媒体设备API');
    recommendations.push('请使用现代浏览器（Chrome 61+, Firefox 55+, Safari 11+）');
  } else if (!hasGetUserMedia) {
    issues.push('浏览器不支持getUserMedia API');
    recommendations.push('请更新浏览器到最新版本');
  }

  // 检查语音识别
  const hasSpeechRecognition = !!(
    window.SpeechRecognition || 
    (window as any).webkitSpeechRecognition
  );

  if (!hasSpeechRecognition) {
    issues.push('浏览器不支持语音识别');
    recommendations.push('语音识别功能需要 Chrome 浏览器支持');
  }

  // 检查Web Audio API
  const hasWebAudio = !!(
    window.AudioContext || 
    (window as any).webkitAudioContext
  );

  if (!hasWebAudio) {
    issues.push('浏览器不支持Web Audio API');
    recommendations.push('音频处理功能需要现代浏览器支持');
  }

  return {
    isSecureContext,
    hasMediaDevices,
    hasGetUserMedia,
    hasSpeechRecognition,
    hasWebAudio,
    userAgent: navigator.userAgent,
    issues,
    recommendations
  };
};

const EnvironmentChecker = () => {
  const [envInfo, setEnvInfo] = useState<any>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const checkEnv = async () => {
    setIsRefreshing(true);
    
    // 稍微延迟以显示加载状态
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const info = checkSimpleEnvironment();
    setEnvInfo(info);
    
    // 如果有问题，自动展开详情
    if (info.issues.length > 0) {
      setIsExpanded(true);
    }
    
    setIsRefreshing(false);
  };

  useEffect(() => {
    checkEnv();
  }, []);

  if (!envInfo) {
    return (
      <Card className="border-blue-200 bg-blue-50/50">
        <CardContent className="flex items-center justify-center p-6">
          <RefreshCw className="w-5 h-5 animate-spin mr-2 text-blue-600" />
          <span className="text-blue-700">正在检查环境兼容性...</span>
        </CardContent>
      </Card>
    );
  }

  const hasIssues = envInfo.issues.length > 0;

  return (
    <div className="space-y-4">
      <Card className={`border-2 ${hasIssues ? 'border-amber-200 bg-amber-50/50' : 'border-green-200 bg-green-50/50'}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between text-lg">
            <div className="flex items-center">
              {hasIssues ? (
                <AlertTriangle className="w-5 h-5 text-amber-600 mr-2" />
              ) : (
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              )}
              <span className={hasIssues ? 'text-amber-800' : 'text-green-800'}>
                环境兼容性检查
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={checkEnv}
              disabled={isRefreshing}
              className="h-8 px-2"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <div className="flex items-center space-x-2">
              <Shield className={`w-4 h-4 ${envInfo.isSecureContext ? 'text-green-600' : 'text-red-600'}`} />
              <span className="text-sm">
                安全环境
                {envInfo.isSecureContext ? (
                  <CheckCircle className="w-3 h-3 text-green-600 ml-1 inline" />
                ) : (
                  <XCircle className="w-3 h-3 text-red-600 ml-1 inline" />
                )}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Mic className={`w-4 h-4 ${envInfo.hasGetUserMedia ? 'text-green-600' : 'text-red-600'}`} />
              <span className="text-sm">
                麦克风API
                {envInfo.hasGetUserMedia ? (
                  <CheckCircle className="w-3 h-3 text-green-600 ml-1 inline" />
                ) : (
                  <XCircle className="w-3 h-3 text-red-600 ml-1 inline" />
                )}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Volume2 className={`w-4 h-4 ${envInfo.hasWebAudio ? 'text-green-600' : 'text-red-600'}`} />
              <span className="text-sm">
                音频API
                {envInfo.hasWebAudio ? (
                  <CheckCircle className="w-3 h-3 text-green-600 ml-1 inline" />
                ) : (
                  <XCircle className="w-3 h-3 text-red-600 ml-1 inline" />
                )}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Info className={`w-4 h-4 ${envInfo.hasSpeechRecognition ? 'text-green-600' : 'text-amber-600'}`} />
              <span className="text-sm">
                语音识别
                {envInfo.hasSpeechRecognition ? (
                  <CheckCircle className="w-3 h-3 text-green-600 ml-1 inline" />
                ) : (
                  <AlertTriangle className="w-3 h-3 text-amber-600 ml-1 inline" />
                )}
              </span>
            </div>
          </div>

          {hasIssues && (
            <div className="space-y-3">
              <Button 
                variant="ghost" 
                className="w-full justify-between p-0 h-auto"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                <span className="text-amber-700 font-medium">
                  检测到 {envInfo.issues.length} 个兼容性问题
                </span>
                <span className="text-xs text-amber-600">
                  {isExpanded ? '收起详情' : '展开详情'}
                </span>
              </Button>
              
              {isExpanded && (
                <div className="space-y-3">
                  {envInfo.issues.map((issue: string, index: number) => (
                    <Alert key={index} className="border-amber-200 bg-amber-50">
                      <AlertTriangle className="h-4 w-4 text-amber-600" />
                      <AlertTitle className="text-amber-800">问题</AlertTitle>
                      <AlertDescription className="text-amber-700">
                        {issue}
                      </AlertDescription>
                    </Alert>
                  ))}
                  
                  {envInfo.recommendations.length > 0 && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-blue-800 mb-2 flex items-center">
                        <Info className="w-4 h-4 mr-1" />
                        建议解决方案：
                      </h4>
                      <ul className="space-y-1">
                        {envInfo.recommendations.map((rec: string, index: number) => (
                          <li key={index} className="text-sm text-blue-700 flex items-start">
                            <span className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {!hasIssues && (
            <div className="text-center py-2">
              <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300">
                ✅ 环境完全兼容，所有功能可正常使用
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EnvironmentChecker;
