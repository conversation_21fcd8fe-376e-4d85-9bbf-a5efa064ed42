import { useState, useCallback, useRef } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useToast } from '@/components/ui/use-toast';
import { localAIService } from '@/services/localAIService';
import { localAudioService } from '@/services/localAudioService';
import { alicloudService } from '@/services/alicloudService';
import { multimodalStoryService } from '@/services/multimodalStoryService';
import { useLocalStoryStorage } from '@/hooks/useLocalStoryStorage';
import { useLocalAudioStorage } from '@/hooks/useLocalAudioStorage';
import { Story } from '@/types/story';
import { useStoriesQueryClient } from './useLocalStoriesQueryClient';

// 使用本地 AI 服务的 hooks
export const useLocalStories = () => {
  const { toast } = useToast();
  const { getLocalStories, saveStoryToLocal } = useLocalStoryStorage();
  const { saveAudioBlob } = useLocalAudioStorage();
  const queryClient = useStoriesQueryClient();

  // 获取本地故事
  const { data: stories = [], isLoading } = useQuery({
    queryKey: ['local-stories'],
    queryFn: () => {
      const localStories = getLocalStories();
      return localStories.map(story => ({
        id: story.id,
        title: story.title,
        description: story.description,
        story: story.story,
        storyChinese: story.storyChinese,
        imageUrl: story.imageUrl,
        hasAudio: story.hasAudio,
        createdAt: new Date(story.createdAt).toISOString(),
        updatedAt: new Date(story.lastAccessed).toISOString(),
      }));
    },
  });

  // 生成多模态故事
  const generateStory = useCallback(async (prompt: string): Promise<Story | null> => {
    try {
      console.log('开始生成故事，提示词:', prompt);
      
      // 检查是否有必要的配置
      const aiConfigStr = localStorage.getItem('ai_config');
      if (!aiConfigStr) {
        throw new Error('请先在系统设置中配置AI服务（页面底部的"配置管理"）');
      }

      // 1. 生成故事内容 - 使用正确的方法名
      const storyResult = await multimodalStoryService.generateMultimodalStory(prompt);
      if (!storyResult) {
        throw new Error('故事生成失败，请检查网络连接和API配置');
      }

      // 2. 准备故事数据
      const story: Story = {
        id: storyResult.id,
        title: storyResult.title,
        description: storyResult.description,
        story: storyResult.story,
        storyChinese: storyResult.storyChinese,
        imageUrl: storyResult.imageUrl,
        hasAudio: !!storyResult.audioBlob && storyResult.audioBlob.size > 0,
        createdAt: storyResult.createdAt,
        updatedAt: new Date().toISOString(),
      };

      // 3. 立即保存故事元数据
      // 即使音频保存失败，故事文本和图片也会被保留
      await saveStoryToLocal(story, story.hasAudio);
      console.log('故事元数据已保存:', story.id);

      // 立即刷新故事列表，让用户能看到新故事
      queryClient.invalidateQueries({ queryKey: ['local-stories'] });

      // 4. 尝试保存音频
      if (story.hasAudio && storyResult.audioBlob) {
        try {
          await saveAudioBlob(story.id, storyResult.audioBlob, story.title);
          console.log('音频已成功保存:', story.id);
          toast({
            title: "音频生成完成",
            description: "故事的音频已准备就绪"
          });
          // 音频成功后无需再次刷新，因为初始状态就是 hasAudio: true
        } catch (audioError) {
          console.error('音频保存失败:', audioError);
          toast({
            variant: "destructive",
            title: "音频保存失败",
            description: "故事内容已保存，但音频未能保存。您可以稍后重新生成。"
          });
          // 音频保存失败，更新故事状态
          story.hasAudio = false;
          await saveStoryToLocal(story, false);
          // 再次刷新列表，更新UI（例如隐藏音频图标）
          queryClient.invalidateQueries({ queryKey: ['local-stories'] });
        }
      }
      
      return story;
    } catch (error) {
      console.error('故事生成失败:', error);
      toast({
        variant: "destructive",
        title: "故事生成失败",
        description: error instanceof Error ? error.message : "请重试"
      });
      return null;
    }
  }, [saveStoryToLocal, saveAudioBlob, toast, queryClient]);

  return {
    stories,
    isLoading,
    generateStory
  };
};

// 使用本地音频服务
interface AudioGenerationParams {
  text: string;
  storyId?: string;
}

interface AudioGenerationResult {
  audioBlob: Blob;
  format: string;
  duration?: number;
}

export const useLocalAudio = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();
  const generationLockRef = useRef<{[key: string]: boolean}>({});

  const generateAudio = useCallback(async (params: AudioGenerationParams): Promise<AudioGenerationResult | null> => {
    // 如果有 storyId，检查是否正在生成
    if (params.storyId && generationLockRef.current[params.storyId]) {
      console.log('音频正在生成中，跳过重复请求');
      return null;
    }

    try {
      if (params.storyId) {
        generationLockRef.current[params.storyId] = true;
      }
      setIsGenerating(true);

      if (!params.text.trim()) {
        throw new Error('文本内容不能为空');
      }

      const result = await localAudioService.generateAudio(params.text);
      return result;
    } catch (error) {
      console.error('音频生成失败:', error);
      toast({
        variant: "destructive",
        title: "音频生成失败",
        description: error instanceof Error ? error.message : "音频生成出错，请重试"
      });
      return null;
    } finally {
      setIsGenerating(false);
      if (params.storyId) {
        delete generationLockRef.current[params.storyId];
      }
    }
  }, [toast]);

  return {
    generateAudio,
    isGenerating
  };
};

// 使用本地聊天服务
export const useLocalChat = () => {
  const { toast } = useToast();
  const [conversationHistory, setConversationHistory] = useState<any[]>([]);

  // 发送消息
  const sendMessageMutation = useMutation({
    mutationFn: async (message: string) => {
      if (!localAIService.isConfigured()) {
        throw new Error('AI 服务未配置，请先在设置中配置 API 密钥');
      }

      const response = await localAIService.chat(message, conversationHistory);
      
      // 更新对话历史
      const newHistory = [
        ...conversationHistory,
        { role: 'user', content: message },
        { role: 'assistant', content: response.response }
      ];
      setConversationHistory(newHistory);

      return response;
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "AI 聊天失败",
        description: error.message,
      });
    }
  });

  // 清空对话历史
  const clearHistory = useCallback(() => {
    setConversationHistory([]);
  }, []);

  return {
    sendMessage: sendMessageMutation.mutate,
    isLoading: sendMessageMutation.isPending,
    conversationHistory,
    clearHistory,
    lastResponse: sendMessageMutation.data?.response,
  };
};

// 使用本地单词定义服务
export const useLocalWordDefinition = () => {
  const { toast } = useToast();

  // 获取单词定义
  const getDefinitionMutation = useMutation({
    mutationFn: async ({ word, context }: { word: string; context: string }) => {
      if (!localAIService.isConfigured()) {
        throw new Error('AI 服务未配置，请先在设置中配置 API 密钥');
      }

      return await localAIService.getWordDefinition(word, context);
    },
    onError: (error: Error) => {
      toast({
        variant: "destructive",
        title: "查询单词失败",
        description: error.message,
      });
    }
  });

  return {
    getDefinition: getDefinitionMutation.mutate,
    isLoading: getDefinitionMutation.isPending,
    definition: getDefinitionMutation.data,
  };
};

// 检查服务状态
export const useServiceStatus = () => {
  const [status, setStatus] = useState({
    ai: false,
    audio: false,
    browserTTS: false,
  });

  const checkStatus = useCallback(async () => {
    const aiConfigured = localAIService.isConfigured();
    const audioConfigured = localAudioService.isConfigured();
    const browserTTSAvailable = 'speechSynthesis' in window;

    setStatus({
      ai: aiConfigured,
      audio: audioConfigured,
      browserTTS: browserTTSAvailable,
    });

    return {
      ai: aiConfigured,
      audio: audioConfigured,
      browserTTS: browserTTSAvailable,
    };
  }, []);

  return {
    status,
    checkStatus,
  };
};
