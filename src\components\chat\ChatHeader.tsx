
import React from 'react';
import { Bo<PERSON>, Trash2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import AIModelSelector from '@/components/AIModelSelector';

interface ChatHeaderProps {
  model: string;
  setModel: (model: string) => void;
  onClear: () => void;
}

const ChatHeader = ({ model, setModel, onClear }: ChatHeaderProps) => {
  return (
    <header className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
      <div className="flex items-center space-x-2">
        <Bot className="w-6 h-6" />
        <h1 className="text-xl font-semibold">AI Assistant</h1>
      </div>
      <div className="flex items-center space-x-2">
        <AIModelSelector 
          selectedModel={model} 
          onModelChange={setModel}
        />
        <Button onClick={onClear} variant="ghost" size="icon" className="text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200">
          <Trash2 className="w-5 h-5" />
        </Button>
      </div>
    </header>
  );
};

export default ChatHeader;
