import React, { useState, useEffect, useCallback } from 'react';
import { 
  HardDrive, 
  Trash2, 
  Download, 
  Upload, 
  RefreshCw,
  Music,
  FileText,
  CircleCheck,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { useLocalStoryStorage } from '@/hooks/useLocalStoryStorage';
import { useLocalAudioStorage } from '@/hooks/useLocalAudioStorage';
import { type LocalStory } from '@/types/story';
import { Checkbox } from "@/components/ui/checkbox"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

const StorageManager = () => {
  const { toast } = useToast();
  const [isClearing, setIsClearing] = useState(false);
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedStories, setSelectedStories] = useState<string[]>([]);
  const [showStoryList, setShowStoryList] = useState(false);

  const [usageInfo, setUsageInfo] = useState({
    storyUsage: 0,
    audioUsage: 0,
    totalUsage: 0,
    storyCount: 0,
    audioCount: 0,
  });

  const {
    getLocalStories,
    getStorageStats,
    cleanupOldStories,
    deleteStory,
    exportStoriesData,
    importStoriesData,
    exportStoriesWithAudio,
    importStoriesWithAudio,
  } = useLocalStoryStorage();

  const [stories, setStories] = useState<LocalStory[]>([]);

  const audioStorage = useLocalAudioStorage();

  // Recalculate stats when dialog is open
  const refreshStats = useCallback(() => {
    const storyStats = getStorageStats();
    const audioUsage = audioStorage.getStorageUsage();
    setUsageInfo({
      storyUsage: storyStats.storageSize,
      audioUsage: audioUsage,
      totalUsage: storyStats.storageSize + audioUsage,
      storyCount: storyStats.totalStories,
      audioCount: storyStats.storiesWithAudio,
    });
    setStories(getLocalStories());
  }, [getStorageStats, audioStorage, getLocalStories]);

  useEffect(() => {
    if (isDialogOpen) {
      refreshStats();
    }
  }, [isDialogOpen, refreshStats]);


  // 清理过期数据
  const handleCleanup = async () => {
    try {
      cleanupOldStories();
      // The max age (30 days) should match the story cleanup age
      await audioStorage.cleanupOldAudio(30);
      toast({
        title: "清理完成",
        description: "已清理过期的故事和音频文件。",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "清理失败",
        description: "清理过程中出现错误。",
      });
    }
  };

  // 清除所有数据
  const clearAllData = async () => {
    try {
      setIsClearing(true);
      
      // 清除 localStorage
      localStorage.clear();
      
      // 清除两个 IndexedDB 数据库
      await Promise.all([
        new Promise<void>((resolve, reject) => {
          const request = indexedDB.deleteDatabase('SpeakSmartAudioDB');
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        }),
        new Promise<void>((resolve, reject) => {
          const request = indexedDB.deleteDatabase('StoryDB');
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        })
      ]);

      toast({
        title: "数据已清除",
        description: "所有本地数据已成功清除，页面将在 2 秒后刷新。",
      });

      // 2秒后刷新页面
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      console.error('清除数据失败:', error);
      toast({
        variant: "destructive",
        title: "清除失败",
        description: "清除数据时出现错误，请重试。",
      });
    } finally {
      setIsClearing(false);
      setIsDialogOpen(false);
      refreshStats(); // Refresh stats after clearing
    }
  };

  // 导出数据
  const handleExport = () => {
    try {
      const data = exportStoriesData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `speak-smart-stories-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "导出成功",
        description: "故事数据已导出到文件。",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "导出失败",
        description: "导出过程中出现错误。",
      });
    }
  };

  // 导入数据
  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = e.target?.result as string;
        importStoriesData(jsonData);
        refreshStats(); // Refresh stats after import
        toast({
          title: "导入成功",
          description: "故事数据已成功导入。",
        });
      } catch (error: any) {
        toast({
          variant: "destructive",
          title: "导入失败",
          description: `导入过程中出现错误: ${error.message}`,
        });
      }
    };
    reader.readAsText(file);
  };

  const handleExportWithAudio = async () => {
    setIsBackingUp(true);
    try {
      const data = await exportStoriesWithAudio(audioStorage);
      if (!data) {
        toast({
          variant: "default",
          title: "没有可备份的数据",
          description: "没有找到带音频的故事可供备份。",
        });
        return;
      }
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `speak-smart-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "备份成功",
        description: "故事和音频数据已备份到文件。",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "备份失败",
        description: `备份过程中出现错误: ${error instanceof Error ? error.message : String(error)}`,
      });
    } finally {
      setIsBackingUp(false);
    }
  };

  const handleImportWithAudio = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const jsonData = e.target?.result as string;
        const success = await importStoriesWithAudio(jsonData, audioStorage);
        if (success) {
          refreshStats();
          toast({
            title: "导入成功",
            description: "故事和音频数据已成功导入。",
          });
        } else {
          throw new Error("导入操作未成功完成。");
        }
      } catch (error: any) {
        toast({
          variant: "destructive",
          title: "导入失败",
          description: `导入过程中出现错误: ${error.message}`,
        });
      }
    };
    reader.readAsText(file);
    // Reset file input to allow importing the same file again
    event.target.value = '';
  };

  const handleStorySelection = (storyId: string) => {
    setSelectedStories(prev => 
      prev.includes(storyId) 
        ? prev.filter(id => id !== storyId) 
        : [...prev, storyId]
    );
  };

  const handleDeleteSelected = () => {
    if (selectedStories.length === 0) {
      toast({
        variant: "default",
        title: "No stories selected",
        description: "Please select at least one story to delete.",
      });
      return;
    }

    const storiesToDelete = stories.filter(story => selectedStories.includes(story.id));
    storiesToDelete.forEach(story => {
      deleteStory(story.id);
      if (story.hasAudio) {
        audioStorage.deleteAudio(story.id);
      }
    });
    setSelectedStories([]);
    refreshStats(); // Refresh stats after deletion
    toast({
      title: "故事已删除",
      description: `成功删除了 ${storiesToDelete.length} 个故事。`,
    });
  };

  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" onClick={() => setIsDialogOpen(true)}>
          <HardDrive className="mr-2 h-4 w-4" />
          存储管理
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>本地存储管理</DialogTitle>
          <DialogDescription>
            管理您保存在浏览器中的故事和音频数据。浏览器存储空间有限（通常为 5-10MB）。
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">存储使用情况</CardTitle>
              <HardDrive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatBytes(usageInfo.totalUsage)}</div>
              <p className="text-xs text-muted-foreground">
                总使用空间 (故事 + 音频)
              </p>
              <div className="mt-4 space-y-2 text-sm">
                  <div className="flex justify-between">
                      <span><FileText className="h-4 w-4 inline-block mr-2" />故事数据</span>
                      <span>{usageInfo.storyCount} 个 / {formatBytes(usageInfo.storyUsage)}</span>
                  </div>
                  <div className="flex justify-between">
                      <span><Music className="h-4 w-4 inline-block mr-2" />音频数据</span>
                      <span>{usageInfo.audioCount} 个 / {formatBytes(usageInfo.audioUsage)}</span>
                  </div>
              </div>
            </CardContent>
          </Card>

          {/* 故事列表管理 */}
          <Card>
            <CardHeader className="pb-3 flex flex-row justify-between items-center cursor-pointer" onClick={() => setShowStoryList(!showStoryList)}>
              <div className="flex flex-col">
                <CardTitle className="text-sm">故事列表管理</CardTitle>
                <CardDescription className="text-xs">选择并删除不需要的故事</CardDescription>
              </div>
              {showStoryList ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </CardHeader>
            {showStoryList && (
              <CardContent>
                <div className="max-h-48 overflow-y-auto space-y-2 pr-2">
                  {stories.map(story => (
                    <div key={story.id} className="flex items-center justify-between p-2 rounded-md hover:bg-gray-100">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={story.id}
                          checked={selectedStories.includes(story.id)}
                          onCheckedChange={() => handleStorySelection(story.id)}
                        />
                        <label htmlFor={story.id} className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                          {story.title}
                        </label>
                      </div>
                      <Badge variant={story.hasAudio ? "secondary" : "outline"}>
                        {story.hasAudio ? <Music className="h-3 w-3" /> : <FileText className="h-3 w-3" />}
                      </Badge>
                    </div>
                  ))}
                </div>
                {stories.length > 0 && (
                  <Button 
                    variant="destructive" 
                    size="sm" 
                    className="w-full mt-4"
                    onClick={handleDeleteSelected}
                    disabled={selectedStories.length === 0}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    删除选中 ({selectedStories.length})
                  </Button>
                )}
              </CardContent>
            )}
          </Card>

          {/* 操作按钮 */}
          <div className="grid grid-cols-2 gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleCleanup}
              className="flex items-center"
            >
              <RefreshCw className="mr-1 h-3 w-3" />
              清理过期
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleExport}
              className="flex items-center"
            >
              <Download className="mr-1 h-3 w-3" />
              导出数据
            </Button>

            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleExportWithAudio}
              disabled={isBackingUp}
              className="flex items-center"
            >
              {isBackingUp ? (
                <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
              ) : (
                <Download className="mr-1 h-3 w-3" />
              )}
              备份所有数据
            </Button>

            <div>
              <label htmlFor="import-complete-file" className="cursor-pointer">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full flex items-center"
                  disabled={isBackingUp}
                  asChild
                >
                  <span>
                    {isBackingUp ? (
                      <RefreshCw className="mr-1 h-3 w-3 animate-spin" />
                    ) : (
                      <Upload className="mr-1 h-3 w-3" />
                    )}
                    导入完整备份
                  </span>
                </Button>
              </label>
              <input
                id="import-complete-file"
                type="file"
                accept=".json"
                onChange={handleImportWithAudio}
                className="hidden"
              />
            </div>
          </div>

          {/* 清除所有数据 */}
          <div>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="destructive"
                  disabled={isClearing}
                  className="w-full flex items-center"
                >
                  {isClearing ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4 mr-2" />
                  )}
                  清除所有数据
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>确认清除数据？</AlertDialogTitle>
                  <AlertDialogDescription>
                    此操作将删除所有本地存储的故事和音频数据。此操作无法撤销。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>取消</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={clearAllData}
                    className="bg-red-500 hover:bg-red-600"
                  >
                    确认清除
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>

          {/* 说明文字 */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>• 故事和音频文件保存在浏览器本地存储中</p>
            <p>• 超过30天未访问的内容会被自动清理</p>
            <p>• 使用"备份所有数据"可以导出完整的故事和音频</p>
            <p>• 导出的数据可以在其他设备上导入</p>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsDialogOpen(false)}>关闭</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StorageManager;
