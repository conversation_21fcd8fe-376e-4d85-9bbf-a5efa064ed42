import React, { useState } from 'react';
import { MessageCircle, BookO<PERSON>, Zap } from 'lucide-react';
import AIChat from '@/components/AIChat';
import RealtimeChat from '@/components/RealtimeChat';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const Chat = () => {
  const [activeTab, setActiveTab] = useState('standard');

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-3">
            <MessageCircle className="text-blue-600" />
            AI 英语助手
          </h1>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            与AI进行智能对话，提升你的英语水平。支持文本聊天和实时语音交流。
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="standard" className="flex items-center gap-2">
              <MessageCircle className="w-4 h-4" />
              标准聊天
            </TabsTrigger>
            <TabsTrigger value="realtime" className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              实时语音
            </TabsTrigger>
          </TabsList>

          <TabsContent value="standard">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="p-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <h2 className="text-2xl font-semibold mb-2">标准文本聊天</h2>
                <p className="opacity-90">
                  支持文本、图片和音频输入，使用阿里云通义千问模型
                </p>
              </div>
              <div className="p-6">
                <AIChat />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="realtime">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="p-6 bg-gradient-to-r from-green-600 to-teal-600 text-white">
                <h2 className="text-2xl font-semibold mb-2">实时语音聊天</h2>
                <p className="opacity-90">
                  实时语音对话，支持语音输入和语音回复，使用 Qwen-Omni 模型
                </p>
              </div>
              <div className="p-6">
                <RealtimeChat />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <BookOpen className="w-12 h-12 text-blue-600 mb-4" />
            <h3 className="text-lg font-semibold mb-2">智能学习</h3>
            <p className="text-gray-600 text-sm">
              AI会根据你的对话内容提供个性化的英语学习建议
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <MessageCircle className="w-12 h-12 text-green-600 mb-4" />
            <h3 className="text-lg font-semibold mb-2">多模态交互</h3>
            <p className="text-gray-600 text-sm">
              支持文字、语音、图片等多种交互方式，让学习更加生动
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <Zap className="w-12 h-12 text-purple-600 mb-4" />
            <h3 className="text-lg font-semibold mb-2">实时反馈</h3>
            <p className="text-gray-600 text-sm">
              实时语音对话功能，获得即时的发音和语法反馈
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chat;
