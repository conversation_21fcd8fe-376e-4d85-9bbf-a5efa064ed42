import { useState, useEffect, useRef } from 'react';
import { useToast } from '@/hooks/use-toast';
import { mediaDevicesUtil } from '@/lib/mediaDevicesUtil';

interface SpeechRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
}

interface UseSpeechRecognitionReturn {
  isListening: boolean;
  transcript: string;
  startListening: () => void;
  stopListening: () => void;
  resetTranscript: () => void;
  isSupported: boolean;
}

// Add type definitions for browser compatibility
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}

export const useSpeechRecognition = (): UseSpeechRecognitionReturn => {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [isSupported, setIsSupported] = useState(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const { toast } = useToast();  useEffect(() => {
    try {
      // Check if we're in a secure context and media devices are supported
      if (!mediaDevicesUtil.isSecureContext()) {
        console.warn('Speech recognition requires a secure context (HTTPS or localhost)');
        return;
      }

      if (!mediaDevicesUtil.isSupported()) {
        console.warn('Media devices are not supported');
        return;
      }

      // Check for SpeechRecognition support
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (!SpeechRecognition) {
        console.warn('SpeechRecognition is not supported');
        return;
      }

      setIsSupported(true);
      recognitionRef.current = new SpeechRecognition();
      
      const recognition = recognitionRef.current;
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'zh-CN';

      recognition.onstart = () => {
        setIsListening(true);
        toast({
          title: "语音识别已启动",
          description: "请开始说话...",
          duration: 2000
        });
      };

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
          } else {
            interimTranscript += result[0].transcript;
          }
        }

        if (finalTranscript) {
          setTranscript(finalTranscript);
        }
      };

      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.error('Speech recognition error:', event.error);
        toast({
          title: "语音识别错误",
          description: `错误: ${event.error}`,
          variant: "destructive"
        });
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
      };

    } catch (error) {
      console.error('Speech recognition setup error:', error);
      setIsSupported(false);
    }

    // Cleanup
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
    };
  }, [toast]);  const startListening = async () => {
    try {
      // Request microphone permission using safe utility
      const stream = await mediaDevicesUtil.requestUserMedia({ audio: true });
      
      if (stream) {
        // Stop the stream immediately as we only needed permission
        stream.getTracks().forEach(track => track.stop());
      }
      
      if (recognitionRef.current && !isListening) {
        recognitionRef.current.start();
      }
    } catch (error) {
      console.error('Microphone access error:', error);
      const errorMessage = mediaDevicesUtil.getErrorMessage(error);
      toast({
        title: "无法访问麦克风",
        description: errorMessage,
        variant: "destructive"
      });
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
    }
  };

  const resetTranscript = () => {
    setTranscript('');
  };

  return {
    isListening,
    transcript,
    startListening,
    stopListening,
    resetTranscript,
    isSupported
  };
};
