import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Mic, MicOff, Image, Send, Volume2, VolumeX } from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isPartial?: boolean;
  audioUrl?: string;
  imageUrl?: string;
}

interface RealtimeChatProps {
  className?: string;
}

export default function RealtimeChat({ className }: RealtimeChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [textInput, setTextInput] = useState('');
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  
  const wsRef = useRef<WebSocket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const currentAudioRef = useRef<HTMLAudioElement | null>(null);

  // WebSocket连接
  const connect = useCallback(() => {
    try {
      wsRef.current = new WebSocket('ws://localhost:3002');
      
      wsRef.current.onopen = () => {
        console.log('WebSocket连接成功');
        setIsConnected(true);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('收到WebSocket消息:', data);
          
          switch (data.type) {
            case 'session.created':
              console.log('会话已创建');
              setIsConnected(true);
              break;
            case 'session.updated':
              console.log('会话已更新');
              break;
            case 'conversation.item.created':
              console.log('对话项已创建');
              break;
            case 'response.created':
              console.log('响应已创建');
              break;
            case 'response.output_item.added':
              console.log('输出项已添加:', data.event);
              // 检查是否包含文本内容
              if (data.event?.item?.type === 'message' && data.event?.item?.content) {
                for (const content of data.event.item.content) {
                  if (content.type === 'text' && content.text) {
                    console.log('在输出项中找到文本:', content.text);
                    handleTextDelta(content.text);
                  }
                }
              }
              break;
            case 'response.content_part.added':
              console.log('内容部分已添加');
              break;
            case 'response.text.delta':
              if (data.delta && typeof data.delta === 'string') {
                handleTextDelta(data.delta);
              } else {
                console.warn('收到非字符串类型的文本delta:', data.delta);
              }
              break;
            case 'response.text.done':
              console.log('文本响应完成');
              break;
            case 'response.audio.delta':
              if (data.delta && typeof data.delta === 'string') {
                handleAudioDelta(data.delta);
              } else {
                console.warn('收到非字符串类型的音频delta:', data.delta);
              }
              break;
            case 'response.audio.done':
              console.log('音频响应完成');
              break;
            case 'response.done':
              console.log('响应完成事件详情:', data.event);
              // 检查响应中是否包含文本内容
              if (data.event?.response?.output) {
                console.log('响应输出内容:', data.event.response.output);
                // 如果有任何文本内容，确保显示
                if (Array.isArray(data.event.response.output)) {
                  for (const item of data.event.response.output) {
                    if (item.type === 'message' && item.content) {
                      for (const content of item.content) {
                        if (content.type === 'text' && content.text) {
                          console.log('在响应完成中找到文本:', content.text);
                          handleTextDelta(content.text);
                        }
                      }
                    }
                  }
                }
              }
              handleResponseDone();
              break;
            case 'error':
              console.error('WebSocket错误:', data.error || data);
              setMessages(prev => [...prev, {
                id: Date.now().toString(),
                type: 'assistant',
                content: `错误: ${data.error?.message || data.message || '未知错误'}`,
                timestamp: new Date()
              }]);
              break;
            default:
              console.log('未处理的消息类型:', data.type, data);
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error, event.data);
        }
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket连接断开');
        setIsConnected(false);
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket错误:', error);
        setIsConnected(false);
      };
    } catch (error) {
      console.error('连接WebSocket失败:', error);
    }
  }, []);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    setIsConnected(false);
  }, []);

  // 处理接收到的文本增量
  const handleTextDelta = useCallback((content: string) => {
    setMessages(prev => {
      const lastMessage = prev[prev.length - 1];
      if (lastMessage && lastMessage.type === 'assistant' && lastMessage.isPartial) {
        // 更新现有的部分消息
        return prev.map((msg, index) => 
          index === prev.length - 1 
            ? { ...msg, content: msg.content + content }
            : msg
        );
      } else {
        // 创建新的部分消息
        return [...prev, {
          id: Date.now().toString(),
          type: 'assistant',
          content,
          timestamp: new Date(),
          isPartial: true
        }];
      }
    });
  }, []);

  // 处理接收到的音频增量
  const handleAudioDelta = useCallback((audioData: string) => {
    try {
      console.log('收到音频delta，长度:', audioData.length);
      // 阿里云返回的是PCM16格式的base64数据，需要转换为WAV才能播放
      const audioBlob = pcm16ToWav(audioData);
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // 立即播放音频
      const audio = new Audio(audioUrl);
      audio.play().catch(error => {
        console.error('音频播放失败:', error);
      });
      
      // 更新最后一条消息的音频
      setMessages(prev => {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage && lastMessage.type === 'assistant') {
          return prev.map((msg, index) => 
            index === prev.length - 1 
              ? { ...msg, audioUrl }
              : msg
          );
        }
        return prev;
      });
    } catch (error) {
      console.error('处理音频增量错误:', error);
    }
  }, []);

  // 处理响应完成
  const handleResponseDone = useCallback(() => {
    setMessages(prev => 
      prev.map(msg => 
        msg.isPartial ? { ...msg, isPartial: false } : msg
      )
    );
  }, []);

  // 音频录制功能
  const startRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: { 
          sampleRate: 16000,
          channelCount: 1 
        } 
      });
      
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      audioChunksRef.current = [];
      
      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
          
          // 转换为PCM16并通过WebSocket发送
          event.data.arrayBuffer().then(buffer => {
            const base64Audio = arrayBufferToBase64(buffer);
            if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
              wsRef.current.send(JSON.stringify({
                type: 'input_audio_buffer.append',
                audio: base64Audio
              }));
            }
          });
        }
      };

      mediaRecorderRef.current.start(100); // 每100ms发送一次音频块
      setIsRecording(true);
    } catch (error) {
      console.error('开始录制错误:', error);
    }
  }, []);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
      
      // 发送录制结束信号
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({ type: 'input_audio_buffer.commit' }));
        // commitAudio 中已经包含了 createResponse，不需要重复发送
      }
    }
  }, [isRecording]);

  // 图片处理
  const handleImageSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const sendTextMessage = useCallback(() => {
    if (!textInput.trim() && !selectedImage) return;
    
    // 添加用户消息
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: textInput || '(已发送图片)',
      timestamp: new Date(),
      imageUrl: imagePreview || undefined
    };
    
    setMessages(prev => [...prev, userMessage]);
    
    // 发送到WebSocket - 使用正确的阿里云消息格式
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const item: any = {
        type: 'conversation.item.create',
        item: {
          type: 'message',
          role: 'user',
          content: []
        }
      };
      
      if (textInput.trim()) {
        item.item.content.push({
          type: 'input_text',
          text: textInput
        });
      }
      
      if (selectedImage) {
        const reader = new FileReader();
        reader.onload = () => {
          item.item.content.push({
            type: 'input_image',
            image: reader.result
          });
          wsRef.current?.send(JSON.stringify(item));
          // 使用简化的response.create格式
          wsRef.current?.send(JSON.stringify({ 
            type: 'response.create',
            response: {
              modalities: ["text", "audio"]
            }
          }));
        };
        reader.readAsDataURL(selectedImage);
      } else {
        wsRef.current.send(JSON.stringify(item));
        // 使用简化的response.create格式
        wsRef.current.send(JSON.stringify({ 
          type: 'response.create',
          response: {
            modalities: ["text", "audio"]
          }
        }));
      }
    }
    
    // 清空输入
    setTextInput('');
    setSelectedImage(null);
    setImagePreview(null);
  }, [textInput, selectedImage, imagePreview]);

  // 音频播放
  const playAudio = useCallback((audioUrl: string) => {
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
    }
    
    const audio = new Audio(audioUrl);
    currentAudioRef.current = audio;
    
    audio.onplay = () => setIsPlaying(true);
    audio.onended = () => setIsPlaying(false);
    audio.onerror = () => setIsPlaying(false);
    
    audio.play().catch(console.error);
  }, []);

  const stopAudio = useCallback(() => {
    if (currentAudioRef.current) {
      currentAudioRef.current.pause();
      currentAudioRef.current = null;
      setIsPlaying(false);
    }
  }, []);

  // 工具函数
  const base64ToBlob = (base64: string, mimeType: string): Blob => {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  };

  const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  };

  // 将PCM16数据转换为WAV格式
  const pcm16ToWav = (base64PCM: string): Blob => {
    const pcmData = atob(base64PCM);
    const pcmBuffer = new ArrayBuffer(pcmData.length);
    const pcmView = new Uint8Array(pcmBuffer);
    for (let i = 0; i < pcmData.length; i++) {
      pcmView[i] = pcmData.charCodeAt(i);
    }

    const sampleRate = 16000;
    const numChannels = 1;
    const bitsPerSample = 16;
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const dataSize = pcmBuffer.byteLength;
    const fileSize = 36 + dataSize;

    const wavBuffer = new ArrayBuffer(44 + dataSize);
    const view = new DataView(wavBuffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, fileSize, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitsPerSample, true);
    writeString(36, 'data');
    view.setUint32(40, dataSize, true);

    // 复制PCM数据
    const wavView = new Uint8Array(wavBuffer);
    wavView.set(pcmView, 44);

    return new Blob([wavBuffer], { type: 'audio/wav' });
  };

  // 副作用
  useEffect(() => {
    return () => {
      disconnect();
      stopAudio();
    };
  }, [disconnect, stopAudio]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          实时多模态对话
          <div className="flex gap-2">
            <Button
              variant={isConnected ? "destructive" : "default"}
              size="sm"
              onClick={isConnected ? disconnect : connect}
            >
              {isConnected ? '断开连接' : '连接'}
            </Button>
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 消息区域 */}
        <div className="h-80 overflow-y-auto border rounded p-4 space-y-3">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs px-3 py-2 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-800'
                } ${message.isPartial ? 'opacity-70' : ''}`}
              >
                {message.imageUrl && (
                  <img 
                    src={message.imageUrl} 
                    alt="发送的图片" 
                    className="w-full rounded mb-2 max-w-48"
                  />
                )}
                <div className="text-sm">{message.content}</div>
                {message.audioUrl && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="mt-2 p-1 h-auto"
                    onClick={() => playAudio(message.audioUrl!)}
                  >
                    <Volume2 className="w-4 h-4" />
                  </Button>
                )}
                <div className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </div>
              </div>
            </div>
          ))}
          {messages.length === 0 && (
            <div className="text-center text-gray-500 mt-8">
              <p>欢迎使用阿里云通义千问实时多模态对话</p>
              <p className="text-sm mt-2">支持语音、文本和图片输入</p>
            </div>
          )}
        </div>

        {/* 图片预览 */}
        {imagePreview && (
          <div className="relative">
            <img src={imagePreview} alt="预览" className="w-20 h-20 object-cover rounded" />
            <Button
              variant="destructive"
              size="sm"
              className="absolute -top-2 -right-2 w-6 h-6 p-0"
              onClick={() => {
                setSelectedImage(null);
                setImagePreview(null);
              }}
            >
              ×
            </Button>
          </div>
        )}

        {/* 输入区域 */}
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Input
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              placeholder="输入消息..."
              onKeyPress={(e) => e.key === 'Enter' && sendTextMessage()}
              disabled={!isConnected}
            />
          </div>
          
          <Button
            variant="outline"
            size="icon"
            onClick={() => fileInputRef.current?.click()}
            disabled={!isConnected}
            title="选择图片"
          >
            <Image className="w-4 h-4" />
          </Button>
          
          <Button
            variant={isRecording ? "destructive" : "outline"}
            size="icon"
            onClick={isRecording ? stopRecording : startRecording}
            disabled={!isConnected}
            title={isRecording ? "停止录音" : "开始录音"}
          >
            {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
          </Button>
          
          <Button
            onClick={sendTextMessage}
            disabled={!isConnected || (!textInput.trim() && !selectedImage)}
            title="发送消息"
          >
            <Send className="w-4 h-4" />
          </Button>
          
          {isPlaying && (
            <Button
              variant="outline"
              size="icon"
              onClick={stopAudio}
              title="停止播放"
            >
              <VolumeX className="w-4 h-4" />
            </Button>
          )}
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageSelect}
          className="hidden"
        />

        {/* 状态指示 */}
        <div className="text-xs text-gray-500 text-center">
          {!isConnected && "请先点击连接按钮建立WebSocket连接"}
          {isConnected && !isRecording && "已连接 - 可以开始对话"}
          {isRecording && "正在录音中..."}
        </div>
      </CardContent>
    </Card>
  );
}