import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import dns from 'dns';
import https from 'https';
import http from 'http';

// Force Node.js to resolve to IPv4 addresses first.
// This is the most reliable way to avoid IPv6 routing issues
// that can cause ETIMEDOUT errors on some networks.
dns.setDefaultResultOrder('ipv4first');

const httpsAgent = new https.Agent({ family: 4 });
const httpAgent = new http.Agent({ family: 4 });

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  optimizeDeps: {
    exclude: ['@ffmpeg/ffmpeg', '@ffmpeg/util']
  },
  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-slot']
        }
      }
    }
  },
  server: {
    host: '0.0.0.0',
    port: 8081,
    proxy: {
      '/unsplash': {
        target: 'https://source.unsplash.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/unsplash/, ''),
        agent: httpsAgent,
      },
      '/api/stories': {
        target: 'http://127.0.0.1:3001',
        changeOrigin: true,
      },
      '/api/puzzles': {
        target: 'http://127.0.0.1:3001',
        changeOrigin: true,
      },
      '/api/audio': {
        target: 'http://127.0.0.1:3001',
        changeOrigin: true,
      },
      '/uploads': {
        target: 'http://127.0.0.1:3001',
        changeOrigin: true,
      },
      '/api/dashscope': {
        target: 'https://dashscope.aliyuncs.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/dashscope/, ''),
        agent: httpsAgent,
        // [!FIX!] 解决请求体在代理中丢失的问题 (最终修复版)
        // 我们需要手动处理请求体并将其写回代理请求
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // Vite/Express的中间件可能已经解析了body
            if (req.body) {
              const bodyData = JSON.stringify(req.body);
              // 打印以确认
              console.log('>>> Proxying Request Body to DashScope:', bodyData);
              // 必须在请求发出前设置Content-Length头
              proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
              // 将body写回代理请求
              proxyReq.write(bodyData);
            }
          });
          proxy.on('error', (err, req, res) => {
            console.error('API Proxy error:', err);
          });
        },
      },
      // More specific proxies for each known Aliyun OSS hostname.
      // This is more robust than a single generic proxy.
      '/api/oss-proxy-wlcb': {
        target: 'http://dashscope-result-wlcb.oss-cn-wulanchabu.aliyuncs.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/oss-proxy-wlcb/, ''),
        agent: httpAgent,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Proxying OSS(wlcb) download to IPv4:', proxyReq.method, proxyReq.path);
          });
          proxy.on('error', (err, req, res) => {
            console.error('OSS(wlcb) Proxy error:', err);
          });
        },
      },
      '/api/oss-proxy-acdr1': {
        target: 'https://dashscope-result-wlcb-acdr-1.oss-cn-wulanchabu-acdr-1.aliyuncs.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/oss-proxy-acdr1/, ''),
        agent: httpsAgent, // Use httpsAgent for https targets
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Proxying OSS(acdr1) download to IPv4:', proxyReq.method, proxyReq.path);
          });
          proxy.on('error', (err, req, res) => {
            console.error('OSS(acdr1) Proxy error:', err);
          });
        },
      },
      '/api/oss-proxy-bj': {
        target: 'https://dashscope-result-bj.oss-cn-beijing.aliyuncs.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/oss-proxy-bj/, ''),
        agent: httpsAgent, // Use httpsAgent for https targets
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Proxying OSS(bj) download to IPv4:', proxyReq.method, proxyReq.path);
          });
          proxy.on('error', (err, req, res) => {
            console.error('OSS(bj) Proxy error:', err);
          });
        },
      },
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
      },
    },
  },
});
