// 直接测试Qwen-Omni-Realtime WebSocket连接
import WebSocket from 'ws';
import { HttpsProxyAgent } from 'https-proxy-agent';
import dotenv from 'dotenv';

dotenv.config();

async function testDirectWebSocket() {
  const apiKey = process.env.DASHSCOPE_API_KEY;
  
  console.log('Testing direct WebSocket connection to Qwen-Omni-Realtime...');
  console.log('API Key prefix:', apiKey ? apiKey.substring(0, 10) + '...' : 'undefined');
  
  // 官方WebSocket端点
  const wsUrl = 'wss://dashscope.aliyuncs.com/api-ws/v1/realtime?model=qwen-omni-turbo-realtime';
  
  const wsOptions = {
    headers: {
      'Authorization': `Bearer ${apiKey}`
    }
  };
  
  // 使用代理进行测试
  if (process.env.HTTPS_PROXY) {
    try {
      wsOptions.agent = new HttpsProxyAgent(process.env.HTTPS_PROXY);
      console.log('Using proxy for WebSocket:', process.env.HTTPS_PROXY);
    } catch (error) {
      console.error('Proxy setup error:', error);
    }
  } else {
    console.log('No proxy configured');
  }
  
  console.log('Connecting to:', wsUrl);
  
  const ws = new WebSocket(wsUrl, wsOptions);
  
  ws.on('open', () => {
    console.log('✅ WebSocket connection opened successfully!');
    
    // 发送会话更新
    const sessionUpdate = {
      type: 'session.update',
      session: {
        modalities: ['text', 'audio'],
        instructions: 'You are a helpful English learning assistant.',
        voice: 'Chelsie',
        turn_detection: {
          type: 'server_vad'
        }
      }
    };
    
    console.log('Sending session update...');
    ws.send(JSON.stringify(sessionUpdate));
    
    // 等待一秒后发送测试消息
    setTimeout(() => {
      const testMessage = {
        type: 'conversation.item.create',
        item: {
          type: 'message',
          role: 'user',
          content: [
            {
              type: 'input_text',
              text: 'Hello, can you introduce yourself?'
            }
          ]
        }
      };
      
      console.log('Sending test message...');
      ws.send(JSON.stringify(testMessage));
      
      // 创建响应
      setTimeout(() => {
        const createResponse = {
          type: 'response.create',
          response: {
            modalities: ['text', 'audio']
          }
        };
        
        console.log('Creating response...');
        ws.send(JSON.stringify(createResponse));
      }, 500);
    }, 1000);
  });
  
  ws.on('message', (data) => {
    try {
      const event = JSON.parse(data.toString());
      console.log('📨 Received event:', event.type);
      
      if (event.type !== 'response.audio.delta') {
        console.log('Event details:', JSON.stringify(event, null, 2));
      } else {
        console.log('Audio delta received, length:', event.delta ? event.delta.length : 0);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
      console.log('Raw message:', data.toString());
    }
  });
  
  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
  });
  
  ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed. Code: ${code}, Reason: ${reason}`);
  });
  
  // 30秒后关闭连接
  setTimeout(() => {
    console.log('Closing connection after 30 seconds...');
    ws.close();
  }, 30000);
}

testDirectWebSocket().catch(console.error);
