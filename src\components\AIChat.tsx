import React, { useState } from 'react';
import { Bot, MessageSquareQuote, BookOpen } from 'lucide-react';
import ChatHeader from './chat/ChatHeader';
import MessageList from './chat/MessageList';
import MessageInput from './chat/MessageInput';
import WordCard from './WordCard';
import Sidebar from './chat/Sidebar';
import { useAIChat } from '@/hooks/useAIChat';

const PromptStarter = ({ icon, title, subtitle, onClick }: { icon: React.ReactNode, title: string, subtitle: string, onClick: () => void }) => (
  <button onClick={onClick} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors w-full">
    <div className="flex items-center gap-4">
      {icon}
      <div>
        <p className="font-semibold text-gray-900 dark:text-gray-100">{title}</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">{subtitle}</p>
      </div>
    </div>
  </button>
);

const AIChat = () => {
  const {
    messages,
    loading,
    sendMessage,
    clearMessages,
    selectedModel,
    setSelectedModel,
    toggleMessagePlayback,
    activeSpeech,
    chatHistory,
    currentChatId,
    startNewChat,
    loadChat,
    deleteChat,
    playTextToSpeech,
    isTtsLoading,
  } = useAIChat();

  const [selectedWord, setSelectedWord] = useState<string | null>(null);
  const [selectedWordContext, setSelectedWordContext] = useState<string>('');

  const handleWordClick = (word: string, context: string) => {
    setSelectedWord(word);
    setSelectedWordContext(context);
  };

  const handlePromptClick = (prompt: string) => {
    sendMessage({ text: prompt });
  };

  const hasMessages = messages.length > 0;

  return (
    <div className="flex h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">
      <div className="w-64 flex-shrink-0 border-r border-gray-200 dark:border-gray-700">
        <Sidebar 
          chatHistory={chatHistory}
          currentChatId={currentChatId}
          onSelectChat={loadChat}
          onNewChat={startNewChat}
          onDeleteChat={deleteChat}
          className='w-64 bg-gray-50 border-r hidden md:flex'
        />
      </div>

      <main className="flex-1 flex flex-col overflow-hidden">
        {hasMessages ? (
          <>
            <ChatHeader model={selectedModel} setModel={setSelectedModel} onClear={clearMessages} />
            <div className="flex-1 p-6 overflow-y-auto">
              <MessageList 
                messages={messages} 
                loading={loading} 
                onWordClick={handleWordClick}
                toggleMessagePlayback={toggleMessagePlayback}
                activeSpeech={activeSpeech}
              />
            </div>
            <div className="p-4 border-t border-gray-200 dark:border-gray-700">
              <MessageInput onSend={sendMessage} loading={loading} />
            </div>
          </>
        ) : (
          <div className="flex-1 flex flex-col items-center justify-center p-4">
            <div className="w-full max-w-2xl mx-auto text-center">
              <Bot className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h2 className="text-3xl font-semibold text-gray-800 dark:text-gray-200">How can I help today?</h2>
              
              <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-4">
                <PromptStarter 
                  icon={<MessageSquareQuote className="w-6 h-6 text-blue-500" />}
                  title="Explain a concept"
                  subtitle="e.g., the difference between 'affect' and 'effect'"
                  onClick={() => handlePromptClick("What's the difference between affect and effect?")}
                />
                <PromptStarter 
                  icon={<BookOpen className="w-6 h-6 text-green-500" />}
                  title="Correct my writing"
                  subtitle="and explain the grammar mistakes"
                  onClick={() => handlePromptClick("Correct this for me and explain the mistakes: 'He don't know nothing about that subject.'")}
                />
              </div>

              <div className="mt-12 w-full">
                <MessageInput onSend={sendMessage} loading={loading} />
              </div>
            </div>
          </div>
        )}
      </main>
      
      {selectedWord && (
        <WordCard
          word={selectedWord}
          context={selectedWordContext}
          isOpen={!!selectedWord}
          onClose={() => setSelectedWord(null)}
          playTextToSpeech={playTextToSpeech}
          isTtsLoading={isTtsLoading}
        />
      )}
    </div>
  );
};

export default AIChat;