import React, { useState } from 'react';
import { useStories } from '@/hooks/useStories';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Loader2, Trash2 } from 'lucide-react';
import { Link } from 'react-router-dom';

const ManageStories = () => {
  const { stories, isLoading, error, setStories } = useStories();
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleSelectAll = (checked: boolean) => {
    if (checked) setSelectedIds(stories.map(s => s.id));
    else setSelectedIds([]);
  };

  const handleSelectOne = (id: string, checked: boolean) => {
    if (checked) setSelectedIds(prev => [...prev, id]);
    else setSelectedIds(prev => prev.filter(selectedId => selectedId !== id));
  };

  const handleDeleteSelected = async () => {
    if (selectedIds.length === 0) {
      alert('请至少选择一个故事来删除。');
      return;
    }
    if (window.confirm(`你确定要删除选中的 ${selectedIds.length} 个故事吗？这个操作无法撤销。`)) {
      setIsDeleting(true);
      try {
        const response = await fetch('/api/stories/batch-delete', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ids: selectedIds }),
        });
        if (!response.ok) throw new Error('批量删除失败');
        setStories(prevStories => prevStories.filter(story => !selectedIds.includes(story.id)));
        setSelectedIds([]);
        alert('删除成功！');
      } catch (err) {
        alert('删除时发生错误。');
      } finally {
        setIsDeleting(false);
      }
    }
  };

  if (isLoading) return <div className="flex justify-center items-center h-screen"><Loader2 className="h-12 w-12 animate-spin text-blue-600" /></div>;
  if (error) return <div className="text-center text-red-500 p-10">{error}</div>;

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">管理故事</h1>
        <Button 
          onClick={handleDeleteSelected} 
          disabled={selectedIds.length === 0 || isDeleting}
          variant="destructive"
        >
          {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Trash2 className="mr-2 h-4 w-4" />}
          删除选中 ({selectedIds.length})
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50px]">
              <Checkbox
                checked={selectedIds.length > 0 && selectedIds.length === stories.length}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            <TableHead>故事标题</TableHead>
            <TableHead>音频</TableHead>
            <TableHead>图片</TableHead>
            <TableHead>创建日期</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {stories.map(story => (
            <TableRow key={story.id}>
              <TableCell>
                <Checkbox
                  checked={selectedIds.includes(story.id)}
                  onCheckedChange={(checked) => handleSelectOne(story.id, !!checked)}
                />
              </TableCell>
              <TableCell className="font-medium">
                <Link to={`/stories/${story.id}`} className="hover:underline">{story.title}</Link>
              </TableCell>
              <TableCell>{story.audioUrl ? '✔️' : '❌'}</TableCell>
              <TableCell>{story.imageUrl && !story.imageUrl.includes('placeholder') ? '✔️' : '❌'}</TableCell>
              <TableCell>{story.createdAt ? new Date(story.createdAt).toLocaleDateString() : 'N/A'}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default ManageStories;