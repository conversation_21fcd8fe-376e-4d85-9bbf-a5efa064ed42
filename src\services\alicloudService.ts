import { SSE } from 'sse.js';
import { toast } from '@/hooks/use-toast';

// 阿里云 AI 服务集成
export interface AliCloudConfig {
  apiKey: string;
}

export interface StoryGenerationResult {
  title: string;
  description: string;
  story: string;
  storyChinese?: string;
  imageUrl?: string;
}

export interface AudioGenerationResult {
  audioBlob: Blob;
  format: string;
  duration?: number;
}

export interface ImageGenerationResult {
  imageUrl: string;
  revisedPrompt?: string;
}

interface TranslationResult {
  translatedText: string;
  originalText?: string; // For image translation, to show what text was recognized
}

class AliCloudService {
  private config: AliCloudConfig | null = null;
  private apiKey: string | null = null;
  // Use a single, consistent proxy path for all API requests.
  private baseUrl = '/api/dashscope';

  setConfig(config: AliCloudConfig) {
    this.config = config;
  }

  isConfigured(): boolean {
    return !!(this.config?.apiKey && this.config.apiKey.trim() !== '');
  }

  // Base request for OpenAI-compatible endpoints (like qwen-turbo)
  private async makeRequest(endpoint: string, data: any) {
    if (!this.isConfigured()) {
      throw new Error('AliCloud API Key is not configured.');
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000);

    try {
      // Always use the proxied base URL
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config!.apiKey}`,
        },
        body: JSON.stringify(data),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorBody;
        try {
          errorBody = await response.json();
        } catch (e) {
          // Ignore if body is not valid JSON
        }
        console.error('AliCloud API Error Response:', errorBody);
        const errorMessage = errorBody?.error?.message || `HTTP error! status: ${response.status}`;
        throw new Error(`阿里云 API 错误 (${response.status}): ${errorMessage}`);
      }

      return await response.json();
    } catch (error: any) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw new Error('请求超时，请检查您的网络连接。');
      }
      throw error;
    }
  }

  // Base request for native DashScope endpoints
  private async makeDashScopeRequest(endpoint: string, data: any, isAsync: boolean = false) {
    if (!this.isConfigured()) {
      throw new Error('AliCloud API Key is not configured.');
    }

    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config!.apiKey}`,
    };

    if (isAsync) {
      headers['X-DashScope-Async'] = 'enable';
    }

    try {
       // Always use the proxied base URL
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        let errorBody;
        try {
          errorBody = await response.json();
        } catch (e) { /* ignore */ }
        console.error('DashScope API Error Response:', errorBody);
        const errorMessage = errorBody?.message || `HTTP error! status: ${response.status}`;
        throw new Error(`DashScope API 错误 (${response.status}): ${errorMessage}`);
      }

      const result = await response.json();
      if (result.code) {
        console.error('DashScope API Error:', result);
        throw new Error(`DashScope API 返回错误: ${result.message} (Code: ${result.code})`);
      }
      return result;
    } catch (error) {
      console.error(`Error in makeDashScopeRequest to ${endpoint}:`, error);
      throw error;
    }
  }

  // Polling logic for async tasks
  private async pollAsyncTask(taskId: string): Promise<any> {
    const pollEndpoint = `/api/v1/tasks/${taskId}`;
    const maxAttempts = 60; // Poll for up to 5 minutes
    const pollInterval = 5000; // 5 seconds

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      await new Promise(resolve => setTimeout(resolve, pollInterval));

      console.log(`Polling task status... (Attempt ${attempt + 1}/${maxAttempts})`);
      
      // Use the proxied base URL for polling as well
      const pollResponse = await fetch(`${this.baseUrl}${pollEndpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config!.apiKey}`,
        },
      });

      if (!pollResponse.ok) {
        console.warn(`Polling request failed (${pollResponse.status}), retrying...`);
        continue;
      }

      const pollResult = await pollResponse.json();
      console.log('Raw poll result:', pollResult); // Log the raw polling result
      const status = pollResult.output?.task_status;

      console.log('Task status:', status);

      if (status === 'SUCCEEDED') {
        // LOG THE ENTIRE OBJECT TO THE BROWSER CONSOLE
        console.log('Image task succeeded. Full API response:', pollResult);

        try {
            // The image URL is in `output.results[0].url`.
            // We need to rewrite it to use our image proxy to avoid CORS/network issues.
            if (pollResult.output?.results?.[0]?.url) {
              // Use the new robust rewrite method
              pollResult.output.results[0].url = this.rewriteOssUrl(
                pollResult.output.results[0].url
              );
            } else {
                console.error("Image generation task succeeded, but the response did not contain a URL at the expected path 'output.results[0].url'.", pollResult);
            }
        } catch (e) {
            console.error("An error occurred while trying to rewrite the image URL.", e, pollResult);
        }

        return pollResult;
      }

      if (status === 'FAILED') {
        console.error('Task failed:', pollResult);
        throw new Error(`异步任务失败: ${pollResult.output?.message || '未知错误'}`);
      }
    }

    throw new Error('异步任务超时，请稍后重试。');
  }

  private rewriteOssUrl(originalUrl: string): string {
    if (!originalUrl) return '';

    // This is a more robust way to handle both http and https URLs.
    try {
        const url = new URL(originalUrl);
        let proxyPath = '';

        if (url.hostname === 'dashscope-result-wlcb.oss-cn-wulanchabu.aliyuncs.com') {
            proxyPath = '/api/oss-proxy-wlcb';
        } else if (url.hostname === 'dashscope-result-wlcb-acdr-1.oss-cn-wulanchabu-acdr-1.aliyuncs.com') {
            proxyPath = '/api/oss-proxy-acdr1';
        } else if (url.hostname === 'dashscope-result-bj.oss-cn-beijing.aliyuncs.com') {
            proxyPath = '/api/oss-proxy-bj';
        }

        if (proxyPath) {
            const rewrittenUrl = proxyPath + url.pathname + url.search;
            console.log(`Rewriting OSS URL to use proxy: ${rewrittenUrl}`);
            return rewrittenUrl;
        }
    } catch (e) {
        // This will catch invalid URLs, and also relative URLs that are already rewritten.
        if (originalUrl.startsWith('/api/')) {
            console.log(`URL is already rewritten: ${originalUrl}`);
            return originalUrl;
        }
    }

    console.warn(`URL NOT REWRITTEN: ${originalUrl}. It does not match any known OSS pattern.`);
    return originalUrl;
  }

  // Text Generation (OpenAI compatible)
  async generateText(prompt: string, model: string = 'qwen-turbo'): Promise<string> {
    const endpoint = '/compatible-mode/v1/chat/completions';
    const data = {
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
      max_tokens: 2000,
      top_p: 0.8,
    };

    try {
      const result = await this.makeRequest(endpoint, data);
      return result.choices?.[0]?.message?.content || '';
    } catch (error) {
      console.error('Text generation failed:', error);
      throw error;
    }
  }

  // Story Generation (uses text generation)
  async generateStory(prompt: string): Promise<StoryGenerationResult> {
    const storyPrompt = `请根据以下提示创作一个英文故事，并提供中文翻译。同时为这个故事生成一个适合的图像描述：

提示：${prompt}

请按照以下JSON格式返回：
{
  "title": "故事标题（英文）",
  "description": "故事简介（中文，50字以内）",
  "story": "完整的英文故事（200-500词）",
  "storyChinese": "中文翻译",
  "imagePrompt": "图像生成提示词（英文，描述故事场景）"
}

要求：
1. 故事要有趣且适合英语学习
2. 使用简单易懂的英语词汇
3. 包含对话和描述
4. 中文翻译要准确自然
5. 图像提示词要生动具体`;

    try {
      const rawResponse = await this.generateText(storyPrompt, 'qwen-plus');
      return this.parseStoryResponse(rawResponse);
    } catch (error) {
      console.error('Story generation failed:', error);
      throw new Error(`故事生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // Image Generation (wanx-v1/v2)
  async generateImage(prompt: string, model: string = 'wanx-v1'): Promise<ImageGenerationResult> {
    const endpoint = '/api/v1/services/aigc/text2image/image-synthesis';
    const data = {
      model,
      input: { prompt },
      parameters: {
        n: 1,
        size: '1024*1024',
      },
    };

    try {
      const createResult = await this.makeDashScopeRequest(endpoint, data, true);
      const taskId = createResult.output?.task_id;
      if (!taskId) {
        throw new Error('Failed to get task_id from response.');
      }

      const pollResult = await this.pollAsyncTask(taskId);
      let imageUrl = pollResult.output?.results?.[0]?.url;
      if (!imageUrl) {
        throw new Error('Image generation succeeded, but no URL was returned.');
      }

      // The URL is already rewritten by pollAsyncTask. No more changes needed here.
      console.log(`Using proxied image URL from async task: ${imageUrl}`);

      return {
        imageUrl,
        revisedPrompt: pollResult.output?.results?.[0]?.revised_prompt,
      };
    } catch (error) {
      console.error(`${model} image generation failed:`, error);
      throw error;
    }
  }

  // Audio Generation (Text-to-Speech)
  async generateAudio(text: string, voice: string = 'Cherry', model: string = 'qwen-tts'): Promise<AudioGenerationResult> {
    const endpoint = '/api/v1/services/aigc/multimodal-generation/generation';
    const data = {
      model,
      input: {
        text: text
      },
      parameters: {
        voice: voice
      }
    };

    try {
      // Per documentation and 403 error, this must be a synchronous call.
      const result = await this.makeDashScopeRequest(endpoint, data, false);

      // The user-provided docs show the URL is in `output.audio.url`.
      const audioUrl = result.output?.audio?.url;

      if (audioUrl) {
        console.log(`Successfully got audio URL: ${audioUrl}`);
        
        // Rewrite the URL to use our new audio proxy.
        const proxyAudioUrl = this.rewriteOssUrl(audioUrl);

        console.log(`Fetching audio via proxy: ${proxyAudioUrl}`);

        const audioResponse = await fetch(proxyAudioUrl);
        if (!audioResponse.ok) {
          throw new Error(`Failed to download audio file from ${proxyAudioUrl}. Status: ${audioResponse.status}`);
        }
        const audioBlob = await audioResponse.blob();
        
        // The docs state the format is 'wav'.
        return { audioBlob, format: 'wav' };
      } else {
        console.error("Audio generation response missing audio URL:", result);
        throw new Error('Audio synthesis succeeded, but no audio URL was returned.');
      }
    } catch (error) {
      console.error('Audio generation failed:', error);
      throw error;
    }
  }

  // Multi-modal analysis (qwen-vl-plus)
  async analyzeImage(imageUrl: string, question: string): Promise<string> {
    const endpoint = '/api/v1/services/aigc/multimodal-generation/generation';
    const data = {
      model: 'qwen-vl-plus',
      input: {
        messages: [
          {
            role: 'user',
            content: [
              { image: imageUrl },
              { text: question }
            ]
          }
        ]
      }
    };

    const result = await this.makeDashScopeRequest(endpoint, data);
    return result.output?.choices?.[0]?.message?.content?.[0]?.text || '';
  }

  getAvailableVoices(): Array<{id: string, name: string, language: string}> {
    return [
      { id: 'Cherry', name: 'Cherry (女声)', language: 'zh-CN' },
      { id: 'Serena', name: 'Serena (女声)', language: 'zh-CN' },
      { id: 'Ethan', name: 'Ethan (男声)', language: 'en-US' },
      { id: 'Chelsie', name: 'Chelsie (女声)', language: 'en-US' }
    ];
  }

  getAvailableTextModels(): Array<{id: string, name: string, description: string}> {
    return [
      { id: 'qwen-turbo', name: 'Qwen-Turbo (推荐)', description: '快速响应，适合日常对话' },
      { id: 'qwen-plus', name: 'Qwen-Plus', description: '平衡性能，适合复杂任务' },
      { id: 'qwen-max', name: 'Qwen-Max', description: '最强性能，适合专业创作' },
    ];
  }

  getAvailableImageModels(): Array<{id: string, name: string, description: string, price: string}> {
    return [
      { id: 'wanx-v1', name: '通义万相 V1 (推荐)', description: '稳定可靠的基础图像生成', price: '0.16元/张' },
      { id: 'wanx2.1-t2i-turbo', name: '通义万相V2.1-Turbo', description: '最新V2版本，速度快、质量高', price: '0.14元/张' },
    ];
  }

  private parseStoryResponse(response: string): StoryGenerationResult & { imagePrompt?: string } {
    try {
      // Find the JSON part of the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        if (parsed.title && parsed.story) {
          return parsed;
        }
      }
      throw new Error("Response is not valid JSON.");
    } catch (error) {
      console.warn("Failed to parse story response as JSON, falling back to text extraction.", error);
      return {
        title: this.extractTitle(response) || 'Generated Story',
        description: 'An interesting story generated by AI.',
        story: response,
      };
    }
  }

  private extractTitle(text: string): string | null {
    const match = text.match(/Title:\s*(.*)/i);
    return match ? match[1] : null;
  }

  async testConnection(): Promise<{text: boolean, image: boolean, audio: boolean}> {
    const results = { text: false, image: false, audio: false };
    try {
      const text = await this.generateText('Hello, world!', 'qwen-turbo');
      if (text) results.text = true;
    } catch (error) { console.error('Text generation test failed:', error); }
    try {
      const image = await this.generateImage('A cat sitting on a cloud', 'wanx-v1');
      if (image.imageUrl) results.image = true;
    } catch (error) { console.error('Image generation test failed:', error); }
    try {
      const audio = await this.generateAudio('Hello, this is a test.', 'Ethan');
      if (audio.audioBlob.size > 0) results.audio = true;
    } catch (error) { console.error('Audio generation test failed:', error); }
    return results;
  }

  // ====================================================================
  // ================== 翻译方法 (通用AI模型实现) ==================
  // ====================================================================
  async translate(
    text: string | null,
    image: string | null,
    sourceLang: string,
    targetLang: string
  ): Promise<TranslationResult> {
    if (!text && !image) {
      throw new Error('Either text or image must be provided for translation.');
    }

    // 1. 图片翻译逻辑: 先 OCR 提取文字, 再走文本翻译流程
    if (image) {
      console.log(`Image translation started. Source: ${sourceLang}, Target: ${targetLang}`);
      try {
        // 使用多模态模型识别图片中的文字
        const ocrPrompt = `Please recognize all the '${sourceLang === 'en' ? 'English' : 'Chinese'}' text in this image. Only return the recognized text.`;
        const ocrText = await this.analyzeImage(image, ocrPrompt);
        
        if (!ocrText || ocrText.trim() === '') {
          return { translatedText: '[No valid text recognized in the image]', originalText: '' };
        }
        
        // 将OCR识别出的文本用于下一步的文本翻译
        const textTranslationResult = await this.translate(ocrText, null, sourceLang, targetLang);
        return {
          ...textTranslationResult,
          originalText: ocrText, // 在最终结果中附加上OCR识别出的原文
        };
      } catch (error) {
        console.error('Image OCR or subsequent translation failed:', error);
        throw new Error(`Image translation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // 2. 纯文本翻译逻辑 (使用通用AI模型进行翻译)
    if (text) {
      console.log(`Text translation started via AI model. Source: ${sourceLang}, Target: ${targetLang}`);
      
      // 构造翻译提示词
      const translatePrompt = `Please translate the following text from ${sourceLang === 'auto' ? 'auto-detected language' : sourceLang} to ${targetLang}. Only return the translated text without any explanations or additional content.

Text to translate: ${text}`;
      
      try {
        // 使用通用AI模型进行翻译
        const translatedText = await this.generateText(translatePrompt, 'qwen-plus');
        
        if (!translatedText) {
            console.warn("Translation result is empty. Using fallback approach.");
            return { translatedText: `[Translation failed for: ${text}]` };
        }
        return { translatedText: translatedText.trim() };
      } catch (error) {
        console.error('Text translation request failed:', error);
        throw new Error(`Text translation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    throw new Error('Invalid translation request. No text or image provided.');
  }
}

// 创建并导出一个单例
const alicloudService = new AliCloudService();
export { alicloudService }; //  <-- 使用命名导出
export type { TranslationResult };
