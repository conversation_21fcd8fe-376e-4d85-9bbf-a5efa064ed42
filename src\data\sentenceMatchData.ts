'''export interface SentencePair {
  id: string;
  english: string;
  chinese: string;
}

export interface SentenceMatchLevel {
  level: number;
  title: string;
  pairs: SentencePair[];
}

export const sentenceMatchData: SentenceMatchLevel[] = [
  {
    level: 1,
    title: 'Level 1: Basic Greetings',
    pairs: [
      { id: 'sm1-1', english: 'Hello, how are you?', chinese: '你好，你怎么样？' },
      { id: 'sm1-2', english: 'Good morning!', chinese: '早上好！' },
      { id: 'sm1-3', english: 'What is your name?', chinese: '你叫什么名字？' },
      { id: 'sm1-4', english: 'My name is <PERSON>.', chinese: '我的名字是李华。' },
      { id: 'sm1-5', english: 'Nice to meet you.', chinese: '很高兴认识你。' },
      { id: 'sm1-6', english: 'Where are you from?', chinese: '你来自哪里？' },
    ],
  },
  {
    level: 2,
    title: 'Level 2: Common Questions',
    pairs: [
        { id: 'sm2-1', english: 'How old are you?', chinese: '你多大了？' },
        { id: 'sm2-2', english: 'What do you do for a living?', chinese: '你是做什么工作的？' },
        { id: 'sm2-3', english: 'Can you help me?', chinese: '你能帮助我吗？' },
        { id: 'sm2-4', english: 'Where is the bathroom?', chinese: '洗手间在哪里？' },
        { id: 'sm2-5', english: 'How much does this cost?', chinese: '这个多少钱？' },
        { id: 'sm2-6', english: 'I don\'t understand.', chinese: '我不明白。' },
    ]
  }
];
''
