import { useEffect } from 'react';
import {
  BrowserRouter as Router,
  Routes,
  Route,
} from "react-router-dom";
import Home from "./pages/Home";
import Translate from "./pages/Translate";
import AIWordPuzzleGenerator from "./components/games/AIWordPuzzleGenerator";
import Login from "./pages/Login";
import Navigation from "./components/Navigation";
import { AuthProvider } from "./hooks/useAuth";
import { Toaster } from "@/components/ui/toaster";
import Stories from "./pages/Stories";
import StoryPage from "./pages/StoryPage";
import Chat from "./pages/Chat";
import Practice from "./pages/Practice";
import Courses from "./pages/Courses";
import Vocabulary from "./pages/Vocabulary";
import Grammar from "./pages/Grammar";
import Pricing from "./pages/Pricing";
import Analysis from "./pages/Analysis";
import Reading from "./pages/Reading";
import Scenarios from "./pages/Scenarios";
import Culture from "./pages/Culture";
import { alicloudService, AliCloudConfig } from './services/alicloudService'; // 引入阿里云服务和配置类型
import FloatingAIChat from './components/FloatingAIChat';
import ManageStories from './pages/ManageStories';

export function App() {
  
  useEffect(() => {
    // 在应用启动时，从本地存储读取已保存的配置，并用它来初始化阿里云服务
    // 这是解决在不同页面间切换导致配置丢失的关键
    const savedConfig = localStorage.getItem('alicloud_config');
    if (savedConfig) {
      try {
        const config: AliCloudConfig = JSON.parse(savedConfig);
        if (config.apiKey) {
          alicloudService.setConfig(config);
          console.log('AliCloud service has been configured globally from localStorage.');
        }
      } catch (error) {
        console.error('Failed to parse AliCloud config from localStorage:', error);
      }
    }
  }, []); // 空依赖数组确保这个 effect 只在应用启动时运行一次

  return (
    <AuthProvider>
      <Router>
        <Navigation />
        <main className="container mx-auto p-4">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/translate" element={<Translate />} />
            <Route path="/games" element={<AIWordPuzzleGenerator />} />
            <Route path="/login" element={<Login />} />
            <Route path="/stories" element={<Stories />} />
            <Route path="/stories/:storyId" element={<StoryPage />} />
            <Route path="/chat" element={<Chat />} />
            <Route path="/practice" element={<Practice />} />
            <Route path="/courses" element={<Courses />} />
            <Route path="/vocabulary" element={<Vocabulary />} />
            <Route path="/grammar" element={<Grammar />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/analysis" element={<Analysis />} />
            <Route path="/reading" element={<Reading />} />
            <Route path="/scenarios" element={<Scenarios />} />
            <Route path="/culture" element={<Culture />} />
            <Route path="/manage-stories" element={<ManageStories />} />
          </Routes>
        </main>
        <FloatingAIChat />
        <Toaster />
      </Router>
    </AuthProvider>
  );
}