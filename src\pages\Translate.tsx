﻿import React, { useState, useRef, useCallback, useEffect } from 'react';
import { ArrowRightLeft, Clipboard, ClipboardCheck, Upload, Volume2, X, Star, Trash2, Mic, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from '@/hooks/use-toast';
import {
  translateText,
  speakText,
  getTranslationHistory,
  getFavoriteTranslations,
  toggleFavorite,
  removeTranslationFromHistory,
  clearTranslationHistory,
  formatTimestamp,
  TranslationRecord
} from '@/services/translationService';

const languages = [
  { value: 'auto', label: '自动检测' },
  { value: 'zh', label: '中文' },
  { value: 'en', label: '英语' },
  { value: 'ja', label: '日语' },
  { value: 'ko', label: '韩语' },
  { value: 'fr', label: '法语' },
  { value: 'de', label: '德语' },
  { value: 'ru', label: '俄语' },
];

const Translate: React.FC = () => {
  const [sourceLang, setSourceLang] = useState('auto');
  const [targetLang, setTargetLang] = useState('en');
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('translate');
  const [history, setHistory] = useState<TranslationRecord[]>([]);
  const [favorites, setFavorites] = useState<TranslationRecord[]>([]);
  const [currentTranslation, setCurrentTranslation] = useState<TranslationRecord | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  useEffect(() => {
    const handleGlobalPaste = (event: ClipboardEvent) => {
      if (document.activeElement?.tagName === 'TEXTAREA') { return; }
      const items = event.clipboardData?.items;
      if (!items) { return; }
      for (const item of items) {
        if (item.type.startsWith('image/')) {
          const blob = item.getAsFile();
          if (blob) {
            toast({ title: "图片已粘贴！" });
            event.preventDefault();
            const reader = new FileReader();
            reader.onloadend = () => {
              setActiveTab('translate');
              setImagePreview(reader.result as string);
              setInputText(''); setOutputText(''); setCurrentTranslation(null);
              window.scrollTo(0, 0);
            };
            reader.readAsDataURL(blob);
            return;
          }
        }
      }
    };
    window.addEventListener('paste', handleGlobalPaste);
    return () => { window.removeEventListener('paste', handleGlobalPaste); };
  }, [toast]);

  const refreshHistoryAndFavorites = useCallback(() => {
    setHistory(getTranslationHistory());
    setFavorites(getFavoriteTranslations());
  }, []);

  useEffect(() => {
    refreshHistoryAndFavorites();
  }, [refreshHistoryAndFavorites]);

  const updateRecordInState = (updatedRecord: TranslationRecord) => {
    if (currentTranslation && currentTranslation.id === updatedRecord.id) {
      setCurrentTranslation(updatedRecord);
    }
    setHistory(prev => prev.map(r => r.id === updatedRecord.id ? updatedRecord : r));
    setFavorites(prev => prev.map(r => r.id === updatedRecord.id ? updatedRecord : r));
  };

  const handleTranslate = async () => {
    if (!inputText.trim() && !imagePreview) {
      toast({ variant: "destructive", title: "请输入文本或上传图片" });
      return;
    }
    setLoading(true);
    setOutputText('');
    setCurrentTranslation(null);
    try {
      const result = await translateText(inputText, imagePreview, sourceLang, targetLang);
      setOutputText(result.translatedText);
      setCurrentTranslation(result);
      refreshHistoryAndFavorites();
      if (result.translatedText) { toast({ title: "翻译成功！" }); }
    } catch (error) {
      toast({ variant: "destructive", title: "翻译失败", description: String(error) });
    } finally {
      setLoading(false);
    }
  };

  const handleSwapLanguages = () => {
    if (sourceLang === 'auto') return;
    setSourceLang(targetLang);
    setTargetLang(sourceLang);
    setInputText(outputText);
    setOutputText(inputText);
    setCurrentTranslation(null);
  };

  const handleCopy = () => {
    if (!outputText) return;
    navigator.clipboard.writeText(outputText);
    setCopied(true);
    toast({ title: "已复制到剪贴板" });
    setTimeout(() => setCopied(false), 2000);
  };

  const handleClearInput = () => {
    setInputText('');
    setImagePreview(null);
    setOutputText('');
    setCurrentTranslation(null);
    if (fileInputRef.current) { fileInputRef.current.value = ''; }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
        setInputText(''); setOutputText(''); setCurrentTranslation(null);
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePasteInTextarea = async (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const items = e.clipboardData?.items;
    if (!items) { return; }
    for (const item of items) {
      if (item.type.startsWith('image/')) {
        const blob = item.getAsFile();
        if (blob) {
          e.preventDefault();
          const reader = new FileReader();
          reader.onloadend = () => {
            setImagePreview(reader.result as string);
            setInputText(''); setOutputText(''); setCurrentTranslation(null);
          };
          reader.readAsDataURL(blob);
          return;
        }
      }
    }
  };

  const handleSpeak = async (text: string, lang: string, recordId?: string) => {
    if (!text || !lang || lang === 'auto') return;
    try {
      const { updatedRecord } = await speakText(text, lang, recordId);
      if (updatedRecord) { updateRecordInState(updatedRecord); }
    } catch (error) {
      toast({ variant: "destructive", title: "语音播放失败", description: String(error) });
    }
  };

  const handleToggleFavorite = (record: TranslationRecord) => {
    const updatedRecord = toggleFavorite(record);
    updateRecordInState(updatedRecord);
  };

  const handleDeleteHistory = (id: string) => {
    removeTranslationFromHistory(id);
    refreshHistoryAndFavorites();
    toast({ title: "历史记录已删除" });
  };

  const handleClearAllHistory = () => {
    clearTranslationHistory();
    refreshHistoryAndFavorites();
    toast({ title: "所有历史记录已清空" });
  };

  const handleHistoryItemClick = (record: TranslationRecord) => {
    setInputText(record.isImage ? '' : record.sourceText);
    setOutputText(record.translatedText);
    setSourceLang(record.sourceLang);
    setTargetLang(record.targetLang);
    setImagePreview(record.isImage ? `data:image/svg+xml;base64,${btoa('<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><text y="55" x="50" text-anchor="middle" font-size="12">Image History</text></svg>')}` : null);
    setCurrentTranslation(record);
    setActiveTab('translate');
  }

  const renderHistoryList = (items: TranslationRecord[]) => {
    if (items.length === 0) {
      return <p className="text-center text-gray-500 mt-12">暂无记录</p>;
    }
    return (
      <div className="space-y-3 mt-4 max-h-[60vh] overflow-y-auto pr-2">
        {items.map(record => (
          <Card key={record.id} className="group p-4 bg-white hover:bg-blue-50/50 transition-colors duration-200">
            <div className="flex justify-between items-start gap-4">
              <div className="flex-1 min-w-0 cursor-pointer" onClick={() => handleHistoryItemClick(record)}>
                <div className="flex items-center justify-between text-xs text-gray-400 mb-2">
                  {/* ================================================================= */}
                  {/* THE FIX IS HERE: Added '?' to prevent crash on incomplete data  */}
                  {/* ================================================================= */}
                  <span className="font-medium">{record.sourceLang?.toUpperCase()} → {record.targetLang?.toUpperCase()}</span>
                  <span>{formatTimestamp(record.timestamp)}</span>
                </div>
                <p className="font-medium text-gray-800 break-words truncate">{record.isImage ? <span className="flex items-center gap-2 text-gray-600"><ImageIcon size={14} /> {record.sourceText}</span> : record.sourceText}</p>
                <p className="text-blue-600 font-semibold break-words mt-1">{record.translatedText}</p>
              </div>
              <div className="flex flex-col items-center justify-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button variant="ghost" size="icon" className="h-8 w-8 shad-button" onClick={(e) => { e.stopPropagation(); handleToggleFavorite(record); }}>
                  <Star size={16} className={record.isFavorite ? 'fill-yellow-400 text-yellow-500' : 'text-gray-400'} />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-400 hover:text-red-500 shad-button" onClick={(e) => { e.stopPropagation(); handleDeleteHistory(record.id); }}>
                  <Trash2 size={16} />
                </Button>
              </div>
            </div>
            <div className="flex items-center gap-2 mt-3 pt-3 border-t border-gray-100">
              {!record.isImage && (
                <Button variant="outline" size="sm" className="shad-button" onClick={(e) => { e.stopPropagation(); handleSpeak(record.sourceText, record.sourceLang, record.id); }} >
                  <Volume2 size={14} className="mr-1.5" /> 原文
                </Button>
              )}
              <Button variant="outline" size="sm" className="shad-button" onClick={(e) => { e.stopPropagation(); handleSpeak(record.translatedText, record.targetLang, record.id); }} >
                <Volume2 size={14} className="mr-1.5" /> 译文
              </Button>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-6xl min-h-screen flex flex-col items-center">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="translate">翻译</TabsTrigger>
          <TabsTrigger value="history">历史</TabsTrigger>
          <TabsTrigger value="favorites">收藏</TabsTrigger>
        </TabsList>
        <TabsContent value="translate">
          <Card className="bg-white/90 backdrop-blur-lg shadow-2xl shadow-blue-100/50 border-gray-200/80 mt-8 rounded-2xl max-w-5xl mx-auto">
            <CardHeader>
              <CardTitle className="text-4xl font-extrabold text-center bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent pb-4 tracking-wide">
                智能翻译中心
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-start min-h-[420px]">
                {/* 输入区 */}
                <div className="flex flex-col gap-6">
                  <Select value={sourceLang} onValueChange={setSourceLang}>
                    <SelectTrigger className="shad-button"><SelectValue /></SelectTrigger>
                    <SelectContent>{languages.map(l => <SelectItem key={l.value} value={l.value}>{l.label}</SelectItem>)}</SelectContent>
                  </Select>
                  <div className="relative group">
                    <Textarea
                      placeholder="输入文本或直接粘贴图片..."
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      onPaste={handlePasteInTextarea}
                      className="h-96 resize-none text-lg p-5 border-2 border-gray-200 focus:border-blue-400 transition-colors rounded-2xl shadow-inner bg-gray-50/80"
                      disabled={!!imagePreview}
                    />
                    <div className="absolute bottom-3 right-5 text-xs text-gray-400 z-20">{inputText.length} / 5000</div>
                    {(inputText || imagePreview) && (
                      <Button variant="ghost" size="icon" className="absolute top-3 right-3 h-8 w-8 shad-button z-30 bg-white/80 hover:bg-white" onClick={handleClearInput}>
                        <X size={18} />
                      </Button>
                    )}
                    {imagePreview && (
                      <div className="absolute inset-0 flex items-center justify-center z-10 pointer-events-none">
                        <img src={imagePreview} alt="Preview" className="max-w-[80%] max-h-[80%] object-contain rounded-xl border border-gray-200 shadow" />
                      </div>
                    )}
                  </div>
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex gap-3">
                      <Button variant="outline" size="icon" className="shad-button" onClick={() => handleSpeak(inputText, sourceLang, currentTranslation?.id)} disabled={!inputText || sourceLang === 'auto'}><Volume2 size={20} /></Button>
                      <Button variant="outline" size="icon" className="shad-button" onClick={() => fileInputRef.current?.click()}><ImageIcon size={20} /></Button>
                      <input type="file" ref={fileInputRef} onChange={handleImageUpload} accept="image/*" className="hidden" />
                    </div>
                  </div>
                </div>
                {/* 输出区 */}
                <div className="flex flex-col gap-6">
                  <div className="flex items-center gap-3">
                    <Button variant="outline" size="icon" className="flex-shrink-0 shad-button" onClick={handleSwapLanguages} disabled={sourceLang === 'auto'}><ArrowRightLeft size={20} /></Button>
                    <Select value={targetLang} onValueChange={setTargetLang}>
                      <SelectTrigger className="shad-button"><SelectValue /></SelectTrigger>
                      <SelectContent>{languages.filter(l => l.value !== 'auto').map(l => <SelectItem key={l.value} value={l.value}>{l.label}</SelectItem>)}</SelectContent>
                    </Select>
                  </div>
                  <div className="relative">
                    <Textarea
                      readOnly
                      value={outputText}
                      className="h-96 resize-none text-lg bg-gray-50/90 p-5 border-2 border-gray-200 rounded-2xl shadow-inner"
                      placeholder="翻译结果..."
                    />
                    {loading && (
                      <div className="absolute inset-0 bg-white/60 backdrop-blur-sm flex items-center justify-center rounded-2xl z-20">
                        <div className="flex flex-col items-center gap-3">
                          <svg className="animate-spin h-10 w-10 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2-647z"></path>
                          </svg>
                          <span className="text-gray-600 text-base">正在努力翻译中...</span>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex gap-3">
                      <Button variant="outline" size="icon" className="shad-button" onClick={() => handleSpeak(outputText, targetLang, currentTranslation?.id)} disabled={!outputText}><Volume2 size={20} /></Button>
                      <Button variant="outline" size="icon" className="shad-button" onClick={handleCopy} disabled={!outputText}>{copied ? <ClipboardCheck size={20} className="text-green-500" /> : <Clipboard size={20} />}</Button>
                    </div>
                    <Button variant="ghost" size="icon" className="shad-button" disabled={!currentTranslation} onClick={() => currentTranslation && handleToggleFavorite(currentTranslation)}>
                      <Star size={24} className={currentTranslation?.isFavorite ? 'fill-yellow-400 text-yellow-500' : ''} />
                    </Button>
                  </div>
                </div>
              </div>
              <div className="text-center mt-10">
                <Button onClick={handleTranslate} disabled={loading || (!inputText.trim() && !imagePreview)} className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 text-white font-bold px-14 py-7 text-xl rounded-full shad-button shadow-lg">
                  {loading ? '翻译中...' : '开始翻译'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="history">
          <Card className="mt-4">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>翻译历史</CardTitle>
              <Button variant="outline" onClick={handleClearAllHistory} disabled={history.length === 0} className="shad-button">
                <Trash2 className="mr-2 h-4 w-4" /> 清空历史
              </Button>
            </CardHeader>
            <CardContent>{renderHistoryList(history)}</CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="favorites">
          <Card className="mt-4">
            <CardHeader><CardTitle>我的收藏</CardTitle></CardHeader>
            <CardContent>{renderHistoryList(favorites)}</CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Translate;