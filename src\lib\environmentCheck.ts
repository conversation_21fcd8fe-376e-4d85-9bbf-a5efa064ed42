// 环境兼容性检查工具
export interface EnvironmentInfo {
  isSecureContext: boolean;
  hasMediaDevices: boolean;
  hasGetUserMedia: boolean;
  hasSpeechRecognition: boolean;
  hasWebAudio: boolean;
  userAgent: string;
  issues: string[];
  recommendations: string[];
}

export const checkEnvironment = (): EnvironmentInfo => {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // 检查安全上下文
  const isSecureContext = 
    window.isSecureContext || 
    window.location.protocol === 'https:' || 
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1';

  if (!isSecureContext) {
    issues.push('需要安全环境（HTTPS）才能使用麦克风功能');
    recommendations.push('请使用 HTTPS 协议访问，或在 localhost 环境下测试');
  }

  // 检查媒体设备API
  const hasMediaDevices = !!navigator.mediaDevices;
  const hasGetUserMedia = hasMediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function';

  if (!hasMediaDevices) {
    issues.push('浏览器不支持媒体设备API');
    recommendations.push('请使用现代浏览器（Chrome 61+, Firefox 55+, Safari 11+）');
  } else if (!hasGetUserMedia) {
    issues.push('浏览器不支持getUserMedia API');
    recommendations.push('请更新浏览器到最新版本');
  }

  // 检查语音识别
  const hasSpeechRecognition = !!(
    window.SpeechRecognition || 
    (window as any).webkitSpeechRecognition
  );

  if (!hasSpeechRecognition) {
    issues.push('浏览器不支持语音识别');
    recommendations.push('语音识别功能需要 Chrome 浏览器支持');
  }

  // 检查Web Audio API
  const hasWebAudio = !!(
    window.AudioContext || 
    (window as any).webkitAudioContext
  );

  if (!hasWebAudio) {
    issues.push('浏览器不支持Web Audio API');
    recommendations.push('音频处理功能需要现代浏览器支持');
  }

  return {
    isSecureContext,
    hasMediaDevices,
    hasGetUserMedia,
    hasSpeechRecognition,
    hasWebAudio,
    userAgent: navigator.userAgent,
    issues,
    recommendations
  };
};

// 安全地获取用户媒体权限
export const safeGetUserMedia = async (constraints: MediaStreamConstraints): Promise<MediaStream> => {
  const env = checkEnvironment();
  
  if (env.issues.length > 0) {
    throw new Error(`环境不兼容: ${env.issues.join(', ')}`);
  }

  try {
    return await navigator.mediaDevices.getUserMedia(constraints);
  } catch (error) {
    if (error instanceof Error) {
      // 提供更友好的错误信息
      switch (error.name) {
        case 'NotAllowedError':
          throw new Error('用户拒绝了麦克风权限请求');
        case 'NotFoundError':
          throw new Error('未找到可用的麦克风设备');
        case 'NotReadableError':
          throw new Error('麦克风设备被其他应用占用');
        case 'OverconstrainedError':
          throw new Error('麦克风设备不支持请求的配置');
        case 'SecurityError':
          throw new Error('安全错误：请确保在安全环境（HTTPS）下访问');
        default:
          throw new Error(`麦克风访问失败: ${error.message}`);
      }
    }
    throw error;
  }
};

// 防止第三方脚本干扰
export const preventThirdPartyInterference = () => {
  // 保护关键的 API
  const originalGetUserMedia = navigator.mediaDevices?.getUserMedia;
  
  if (originalGetUserMedia) {
    // 创建一个受保护的版本
    const protectedGetUserMedia = originalGetUserMedia.bind(navigator.mediaDevices);
    
    // 监听可能的干扰
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      if (message.includes('getUserMedia') && message.includes('content.js')) {
        // 忽略来自 content.js 的 getUserMedia 错误
        return;
      }
      originalConsoleError.apply(console, args);
    };
  }
};
